"""
AssignAppointment Use Case - Stage 1 of healthcare scheduling optimization.

This use case assigns providers and dates to appointments using the Timefold solver.
It is the first stage of the 2-stage optimization process:
1. Assignment Solver: Assigns providers and dates to appointments (this use case)
2. Day Plan Solver: Optimizes timing and routing within a day (DayPlan use case)
"""

import logging
import os
import sys
import time
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from uuid import uuid4

from timefold.solver import SolverFactory, SolutionManager
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver.domain import (
    PlanningId, PlanningVariable, PlanningEntityCollectionProperty, 
    ProblemFactCollectionProperty, ValueRangeProvider, PlanningScore
)
from timefold.solver.score import HardSoftScore

from loguru import logger

# Import domain entities
from src.domain.entities.appointment import (
    AppointmentData, AppointmentAssignment, AppointmentSchedule
)
from src.domain.entities.provider import Provider
from src.domain.entities.consumer import Consumer
from src.domain.models.config import SchedulerConfig, ServiceConfig, BatchAssignmentResult, AssignmentResult

# Import infrastructure components
from src.infrastructure.config.config_manager import ConfigManager
from src.infrastructure.data.data_loader import create_demo_data
from src.infrastructure.bridges.scheduler_bridge import define_constraints


class AssignAppointmentUseCase:
    """Use case for assigning providers and dates to appointments."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, daemon_mode: bool = False):
        """Initialize the assignment use case."""
        self.config_manager = config_manager or ConfigManager()
        self.scheduler_config = self.config_manager.get_scheduler_config()
        self.service_configs = self.config_manager.get_all_service_configs()
        self.daemon_mode = daemon_mode
        
        # Initialize solver with proper configuration for large datasets
        solver_config = SolverConfig(
            solution_class=AppointmentSchedule,
            entity_class_list=[AppointmentAssignment],  # Provider is not a planning entity
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=120),  # 2 minutes for large dataset
                unimproved_spent_limit=Duration(seconds=30)  # Stop if no improvement for 30 seconds
            )
        )
        
        self.solver_factory = SolverFactory.create(solver_config)
        self.solution_manager = SolutionManager.create(self.solver_factory)
        
        logger.info("AssignAppointment use case initialized with solver config")
    
    def execute(self, target_date: Optional[date] = None, service_type: Optional[str] = None) -> Dict[str, Any]:
        """Execute the appointment assignment use case."""
        start_time = time.time()
        
        logger.info("[START] ASSIGNMENT USE CASE STARTED")
        logger.info(f"[INFO] Target Date: {target_date or 'Today'}")
        logger.info(f"[INFO] Service Type: {service_type or 'All Services'}")
        
        try:
            # STAGE 1: Load demo data
            logger.info("[INFO] STAGE 1: Loading Data")
            demo_data = create_demo_data()
            providers = demo_data["providers"]
            consumers = demo_data["consumers"]
            appointments = demo_data["appointments"]
            
            logger.info(f"[INFO] Initial data loaded: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")
            
            # STAGE 2: Filter appointments by service type
            if service_type:
                logger.info(f"[INFO] STAGE 2A: Filtering by Service Type ({service_type})")
                appointments = self._filter_appointments_by_service(appointments, service_type)
                logger.info(f"[INFO] Filtered to {len(appointments)} appointments for service type: {service_type}")
            else:
                logger.info("[INFO] STAGE 2A: No service type filter applied")
                logger.info(f"[INFO] Using all {len(appointments)} appointments")
            
            # STAGE 3: Filter appointments by date range
            if target_date:
                logger.info(f"[INFO] STAGE 2B: Filtering by Date Range")
                logger.info(f"[INFO] Date range: {target_date} to {target_date + timedelta(days=self.scheduler_config.rolling_window_days - 1)}")
                appointments = self._filter_appointments_by_date_range(appointments, target_date)
                logger.info(f"[INFO] Filtered to {len(appointments)} appointments for date range")
            else:
                logger.info("[INFO] STAGE 2B: No date filter applied")
                logger.info(f"[INFO] Using all {len(appointments)} appointments")
            
            if not appointments:
                logger.warning("[WARN] No appointments to assign after filtering")
                result = {
                    "success": True,
                    "message": "No appointments to assign",
                    "assignments": [],
                    "processing_time": time.time() - start_time
                }
                self._handle_completion(result)
                return result
            
            # STAGE 4: Create planning entities
            logger.info("[INFO] STAGE 3: Creating Planning Entities")
            domain_assignments = self._create_domain_assignments(appointments)
            planning_assignments = self._create_planning_assignments(appointments)
            logger.info(f"[INFO] Created {len(planning_assignments)} planning entities")
            
            # STAGE 5: Create available dates
            logger.info("[INFO] STAGE 4: Creating Available Dates")
            available_dates = self._create_available_dates(target_date)
            logger.info(f"[INFO] Created {len(available_dates)} available dates: {[d.strftime('%Y-%m-%d') for d in available_dates]}")
            
            # STAGE 6: Create solution
            logger.info("[INFO] STAGE 5: Creating Optimization Solution")
            # Create domain solution first
            domain_solution = AppointmentSchedule(
                id=str(uuid4()),
                providers=providers,
                available_dates=available_dates,
                appointment_assignments=domain_assignments
            )
            # Convert to planning solution for solver
            solution = self._create_planning_schedule(domain_solution)
            logger.info(f"[INFO] Solution created with {len(planning_assignments)} assignments, {len(available_dates)} available dates")
            
            # STAGE 7: Solve the problem
            logger.info("[INFO] STAGE 6: Starting Optimization Solver")
            solved_solution = self._solve_assignment_problem(solution, service_type)
            logger.info("[INFO] Optimization solver completed")
            
            # STAGE 8: Process results
            logger.info("[INFO] STAGE 7: Processing Results")
            results = self._process_assignment_results(solved_solution, consumers, start_time)
            
            logger.info("[COMPLETE] ASSIGNMENT USE CASE COMPLETED")
            logger.info(f"[INFO] Total processing time: {results['processing_time']:.2f} seconds")
            
            self._handle_completion(results)
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error in AssignAppointment use case: {e}", exc_info=True)
            result = {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
            self._handle_completion(result)
            return result
    
    def _filter_appointments_by_service(self, appointments: List[AppointmentData], service_type: str) -> List[AppointmentData]:
        """Filter appointments by service type based on required skills."""
        service_config = self.service_configs.get(service_type)
        if not service_config:
            logger.warning(f"No service config found for {service_type}, using all appointments")
            return appointments
        
        filtered_appointments = []
        for appointment in appointments:
            # Check if appointment requires any of the service skills
            if any(skill in appointment.required_skills for skill in service_config.required_skills):
                filtered_appointments.append(appointment)
        
        return filtered_appointments
    
    def _filter_appointments_by_date_range(self, appointments: List[AppointmentData], target_date: date) -> List[AppointmentData]:
        """Filter appointments by date range starting from target date."""
        start_date = target_date
        end_date = target_date + timedelta(days=self.scheduler_config.rolling_window_days - 1)
        
        filtered_appointments = []
        for appointment in appointments:
            if start_date <= appointment.appointment_date <= end_date:
                filtered_appointments.append(appointment)
        
        return filtered_appointments
    
    def _create_domain_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create domain assignment objects."""
        assignments = []
        for appointment in appointments:
            assignment = AppointmentAssignment(
                id=f"assignment_{appointment.id}",
                appointment_data=appointment,
                provider=None,  # Will be assigned by solver
                assigned_date=None  # Will be assigned by solver
            )
            assignments.append(assignment)
        return assignments
    
    def _create_planning_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create planning assignment entities for the solver."""
        return self._create_domain_assignments(appointments)
    
    def _create_available_dates(self, target_date: Optional[date]) -> List[date]:
        """Create available dates for assignment."""
        if target_date is None:
            target_date = date.today()
        
        available_dates = []
        for i in range(self.scheduler_config.rolling_window_days):
            available_dates.append(target_date + timedelta(days=i))
        
        return available_dates
    
    def _create_planning_schedule(self, domain_solution: AppointmentSchedule) -> AppointmentSchedule:
        """Convert domain solution to planning solution for solver."""
        # For now, just return the domain solution as is
        # In a more complex implementation, this would convert to planning entities
        return domain_solution
    
    def _solve_assignment_problem(self, solution: AppointmentSchedule, service_type: Optional[str]) -> AppointmentSchedule:
        """Solve the assignment problem using Timefold."""
        logger.info(f"Solving assignment problem with {len(solution.appointment_assignments)} appointments")
        
        # Create solver
        solver = self.solver_factory.build_solver()
        
        # Solve the problem
        solved_solution = solver.solve(solution)
        
        # Get solution info
        solution_info = self.solution_manager.explain(solved_solution)
        logger.info(f"Solution score: {solved_solution.score}")
        logger.info(f"Solution info: {solution_info}")
        
        return solved_solution
    
    def _process_assignment_results(self, solution: AppointmentSchedule, consumers: List[Consumer], start_time: float) -> Dict[str, Any]:
        """Process the assignment results and create response."""
        processing_time = time.time() - start_time
        
        # Create results list
        results = []
        assigned_count = 0
        unassigned_count = 0
        
        for assignment in solution.appointment_assignments:
            if assignment.provider and assignment.assigned_date:
                assigned_count += 1
                result = AssignmentResult(
                    appointment_id=str(assignment.appointment_data.id),
                    patient_id=str(assignment.appointment_data.consumer_id),
                    provider_id=str(assignment.provider.id),
                    time_slot_id=f"{assignment.assigned_date}",
                    score=0.0,  # Will be calculated by constraint checking
                    constraints_satisfied=[],
                    constraints_violated=[]
                )
                results.append(result)
            else:
                unassigned_count += 1
        
        # Create batch result
        batch_result = BatchAssignmentResult(
            batch_id=f"assignment_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            total_appointments=len(solution.appointment_assignments),
            assigned_appointments=assigned_count,
            unassigned_appointments=unassigned_count,
            average_score=0.0,  # Will be calculated
            processing_time_seconds=processing_time,
            results=results
        )
        
        return {
            "success": True,
            "batch_result": batch_result,
            "assignments": [str(assignment) for assignment in solution.appointment_assignments],
            "processing_time": processing_time
        }
    
    def _handle_completion(self, results: Dict[str, Any]):
        """Handle completion of the use case."""
        if self.daemon_mode:
            # In daemon mode, we might want to publish results or store them
            logger.info("Daemon mode: Results ready for API consumption")
        else:
            # In standalone mode, just log the results
            logger.info(f"Standalone mode: Results completed - {results.get('success', False)}")


def main(daemon_mode: bool = False):
    """Main entry point for the assign appointments use case."""
    use_case = AssignAppointmentUseCase(daemon_mode=daemon_mode)
    return use_case.execute()


if __name__ == "__main__":
    main() 