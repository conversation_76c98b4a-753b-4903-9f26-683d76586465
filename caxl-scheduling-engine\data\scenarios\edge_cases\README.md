# Edge Cases Scenario

## Purpose
Test various edge cases, constraint violations, and boundary conditions to ensure robust scheduling.

## Complexity
High - designed to stress test the scheduling engine with challenging scenarios.

## Features Tested
- Provider capacity overload
- Skill mismatches
- Geographic constraints
- Urgent appointment handling
- Time conflicts
- Provider unavailability
- Complex dependencies

## Scenario Description
This scenario includes challenging cases:
- **Capacity Overload**: More appointments than providers can handle
- **Skill Gaps**: Appointments requiring skills no provider has
- **Geographic Conflicts**: Appointments outside provider service areas
- **Time Conflicts**: Overlapping appointments for same provider
- **Urgent Priority**: Urgent appointments competing with regular ones
- **Provider Conflicts**: Providers with blacklisted consumers
- **Dependencies**: Appointments that must be scheduled together

## Expected Outcomes
- System should handle gracefully when not all appointments can be scheduled
- Urgent appointments should get priority
- Skill mismatches should be clearly identified
- Geographic violations should be logged
- Capacity overload should be managed appropriately

## Configuration
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: true
enable_workload_balancing: true
enable_patient_preferences: true
enable_provider_capacity_management: true
enable_route_optimization: false
```

## Edge Cases Included
1. **Capacity Overload**: 15 appointments for 3 providers (max 6 each)
2. **Skill Mismatch**: Appointment requiring "cardiac_specialist" skill
3. **Geographic Conflict**: Appointment 50 miles from nearest provider
4. **Time Conflict**: Two 2-hour appointments at same time
5. **Provider Blacklist**: Provider who can't serve specific patient
6. **Urgent vs Regular**: Mix of urgent and regular appointments
7. **Dependency Chain**: 3 appointments that must be sequential

## Usage
```bash
# Run edge case testing
python -m src.main --scenario edge_cases

# Or via API
curl -X POST http://localhost:8000/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{"scenario": "edge_cases"}'
```

## Expected Log Messages
- "Capacity constraint violated for provider X"
- "No provider available with required skills: cardiac_specialist"
- "Geographic constraint violated for appointment Y"
- "Time conflict detected for provider Z"
- "Urgent appointment prioritized over regular appointment" 