from timefold.solver.score import (constraint_provider, HardSoftScore, Joiners,
                                   ConstraintFactory, Constraint)

from .domain import Container, Package, PackageAssignment, LoadingSolution

# Constraint names for easy identification
WEIGHT_CAPACITY_EXCEEDED = "weightCapacityExceeded"
VOLUME_CAPACITY_EXCEEDED = "volumeCapacityExceeded"
UNASSIGNED_PACKAGE = "unassignedPackage"
FRAGILE_PACKAGE_OVERLOAD = "fragilePackageOverload"
TEMPERATURE_SENSITIVE_MIXING = "temperatureSensitiveMixing"
WEIGHT_DISTRIBUTION_OPTIMIZATION = "weightDistributionOptimization"
CONTAINER_UTILIZATION = "containerUtilization"
PRIORITY_PACKAGE_PLACEMENT = "priorityPackagePlacement"


@constraint_provider
def define_constraints(constraint_factory: ConstraintFactory):
    return [
        # Hard constraints (must be satisfied)
        unassigned_package(constraint_factory),
        fragile_package_overload(constraint_factory),
        temperature_sensitive_mixing(constraint_factory),

        # Soft constraints (preferences)
        weight_distribution_optimization(constraint_factory),
        container_utilization(constraint_factory),
        priority_package_placement(constraint_factory),
    ]


def unassigned_package(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Hard constraint: All packages must be assigned to a container.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is None)
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment: 1)
            .as_constraint(UNASSIGNED_PACKAGE))


def fragile_package_overload(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Hard constraint: Fragile packages should not be overloaded with heavy packages.
    Penalty based on fragility factor and weight of packages above.
    """
    return (constraint_factory
            .for_each_unique_pair(PackageAssignment,
                                 Joiners.equal(lambda assignment: assignment.container))
            .filter(lambda assignment1, assignment2: 
                   assignment1.container is not None and
                   assignment2.container is not None and
                   assignment1.package.fragility > 1.5 and 
                   assignment2.package.weight > 50.0)
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment1, assignment2: 
                     int(assignment1.package.fragility * assignment2.package.weight / 10))
            .as_constraint(FRAGILE_PACKAGE_OVERLOAD))


def temperature_sensitive_mixing(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Hard constraint: Temperature sensitive packages should not be mixed with non-temperature sensitive ones.
    """
    return (constraint_factory
            .for_each_unique_pair(PackageAssignment,
                                 Joiners.equal(lambda assignment: assignment.container))
            .filter(lambda assignment1, assignment2: 
                   assignment1.container is not None and
                   assignment2.container is not None and
                   assignment1.package.temperature_sensitive != assignment2.package.temperature_sensitive)
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment1, assignment2: 1)
            .as_constraint(TEMPERATURE_SENSITIVE_MIXING))


def weight_distribution_optimization(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Soft constraint: Optimize weight distribution across containers.
    Reward balanced weight distribution.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None)
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: int(assignment.package.weight))
            .as_constraint(WEIGHT_DISTRIBUTION_OPTIMIZATION))


def container_utilization(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Soft constraint: Optimize container utilization (both weight and volume).
    Reward high utilization but penalize over-utilization.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None)
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: int(assignment.package.weight + assignment.package.volume * 100))
            .as_constraint(CONTAINER_UTILIZATION))


def priority_package_placement(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Soft constraint: High priority packages should be placed in high priority containers.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: (assignment.container is not None and
                                      assignment.package.priority >= 3 and
                                      assignment.container.priority >= 3))
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: assignment.package.priority * assignment.container.priority)
            .as_constraint(PRIORITY_PACKAGE_PLACEMENT))


# Alternative approach using weight-based scoring functions
def create_weight_based_constraints(constraint_factory: ConstraintFactory) -> list[Constraint]:
    """
    Alternative constraint provider that demonstrates different weight-based approaches.
    """
    return [
        # Weight-based hard constraints
        weight_capacity_with_dynamic_penalty(constraint_factory),
        weight_balance_across_containers(constraint_factory),
        weight_priority_scoring(constraint_factory),
    ]


def weight_capacity_with_dynamic_penalty(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Hard constraint with dynamic penalty based on weight excess.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None and
                   assignment.package.weight > assignment.container.max_weight * 0.9)
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment: int(assignment.package.weight * 1.5))
            .as_constraint("DynamicWeightPenalty"))


def weight_balance_across_containers(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Soft constraint to balance weight distribution across containers.
    """
    return (constraint_factory
            .for_each_unique_pair(PackageAssignment,
                                 Joiners.equal(lambda assignment: assignment.container))
            .filter(lambda assignment1, assignment2: 
                   assignment1.container is not None and
                   assignment2.container is not None)
            .penalize(HardSoftScore.ONE_SOFT,
                     lambda assignment1, assignment2: 
                     int(abs(assignment1.package.weight - assignment2.package.weight) / 10))
            .as_constraint("WeightBalance"))


def weight_priority_scoring(constraint_factory: ConstraintFactory) -> Constraint:
    """
    Soft constraint that rewards packages based on weight and priority combination.
    """
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None)
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: 
                   int(assignment.package.weight * assignment.package.priority / 10))
            .as_constraint("WeightPriorityScoring")) 