"""
Configuration models for healthcare scheduling optimization.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ServiceConfig(BaseModel):
    """Configuration for a specific service type."""
    service_type: str
    required_skills: List[str]
    geographic_radius_miles: float = 25.0
    max_daily_appointments_per_provider: int = 8
    max_weekly_hours_per_provider: int = 40
    continuity_weight: float = 0.8
    workload_balance_weight: float = 0.6
    geographic_clustering_weight: float = 0.4
    patient_preference_weight: float = 0.7
    capacity_threshold_percentage: float = 0.9


class SchedulerConfig(BaseModel):
    """Main scheduler configuration."""
    rolling_window_days: int = 7
    batch_size: int = 100
    max_solving_time_seconds: int = 300
    config_folder: str = "config"
    log_level: str = "INFO"

    # Feature Toggles 
    enable_geographic_clustering: bool = True
    enable_continuity_of_care: bool = True
    enable_workload_balancing: bool = True
    enable_patient_preferences: bool = False
    enable_provider_capacity_management: bool = False
    enable_healthcare_task_sequencing: bool = False
    enable_travel_time_optimization: bool = False
    enable_break_time_management: bool = False
    enable_route_optimization: bool = False
    
    # Advanced Traffic Integration (Enterprise Feature)
    enable_advanced_traffic_integration: bool = False
    
    # Traffic Integration Configuration
    traffic_integration: Optional[Dict[str, Any]] = None
    
    # Basic Traffic Model Configuration
    traffic_model: Optional[Dict[str, Any]] = None


class GeographicCluster(BaseModel):
    """Geographic cluster for route efficiency."""
    center_latitude: float
    center_longitude: float
    radius_miles: float
    provider_ids: List[str]
    patient_ids: List[str]


class AssignmentResult(BaseModel):
    """Result of appointment assignment."""
    appointment_id: str
    patient_id: str
    provider_id: str
    time_slot_id: str
    score: float
    constraints_satisfied: List[str]
    constraints_violated: List[str]
    assignment_date: datetime = Field(default_factory=datetime.now)


class BatchAssignmentResult(BaseModel):
    """Result of batch appointment assignment."""
    batch_id: str
    total_appointments: int
    assigned_appointments: int
    unassigned_appointments: int
    average_score: float
    processing_time_seconds: float
    results: List[AssignmentResult]
    created_at: datetime = Field(default_factory=datetime.now) 