########################
# Timefold Solver properties
########################

# The solver runs for 30 seconds. To run for 5 minutes use "5m" and for 2 hours use "2h".
timefold.solver.termination.spent-limit=30s

# When benchmarking, each individual solver runs for 15 seconds. To run for 5 minutes use "5m" and for 2 hours use "2h".
timefold.benchmark.solver.termination.spent-limit=15s

# To change how many solvers to run in parallel
# timefold.solver-manager.parallel-solver-count=4

# Temporary comment this out to detect bugs in your code (lowers performance)
# timefold.solver.environment-mode=FULL_ASSERT

# Temporary comment this out to return a feasible solution as soon as possible
# quarkus.timefold.solver.termination.best-score-limit=0hard/*soft

# To see what Timefold is doing, turn on DEBUG or TRACE logging.
logging.level.ai.timefold.solver=INFO

# XML file for power tweaking, defaults to solverConfig.xml (directly under src/main/resources)
# timefold.solver-config-xml=org/.../timeTableSolverConfig.xml

########################
# Spring Boot properties
########################

# Make it easier to read Timefold logging
logging.pattern.console=%d{HH:mm:ss.SSS} %clr(${LOG_LEVEL_PATTERN:%5p}) %blue([%-15.15t]) %m%n
