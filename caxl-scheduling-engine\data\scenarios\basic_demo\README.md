# Basic Demo Scenario

## Purpose
Simple demonstration of core functionality for initial demos and basic feature overview.

## Complexity
Low - suitable for new audiences and basic feature demonstrations.

## Features Tested
- Basic appointment assignment
- Skill matching between providers and appointments
- Geographic area constraints
- Simple capacity management
- Provider availability

## Scenario Description
This scenario includes:
- 3 providers (RN, LPN, CNA) with different skills and availability
- 5 patients with various care needs
- 8 appointments requiring different skill sets
- Geographic clustering within Manhattan
- Basic capacity constraints

## Expected Outcomes
- All appointments should be assigned to appropriate providers
- Skill matching should be 100%
- Geographic proximity should be maintained
- No provider should exceed capacity limits

## Configuration
```yaml
enable_geographic_clustering: true
enable_continuity_of_care: false
enable_workload_balancing: true
enable_patient_preferences: false
enable_provider_capacity_management: true
enable_route_optimization: false
```

## Usage
```bash
# Run with this scenario
python -m src.main --scenario basic_demo

# Or via API
curl -X POST http://localhost:8000/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{"scenario": "basic_demo"}'
``` 