"""
Consumer domain entities for healthcare scheduling optimization.
"""

from dataclasses import dataclass, field
from datetime import time
from typing import Any, Dict, List, Optional, Annotated
from uuid import UUID

from pydantic import BaseModel
from timefold.solver.domain import PlanningId


class ConsumerPreferences(BaseModel):
    """Consumer preferences for healthcare provider assignment."""

    # Scheduling preferences
    preferred_days: List[str] = []                     # Days consumer prefers to receive care
    preferred_hours: Optional[tuple[time, time]] = None    # Time window consumer prefers (start, end)
    unavailable_days: List[str] = []                   # Days consumer is unavailable
    unavailable_hours: List[tuple[time, time]] = []        # Time windows when consumer is unavailable

    # Cultural and language preferences
    cultural_considerations: List[str] = []                # Cultural needs/preferences
    language: Optional[str] = None                         # Preferred language (no default assumption)
    gender_preference: Optional[str] = None                # Preferred provider gender

    # Provider preferences
    preferred_providers: List[str] = []                    # Specific providers consumer prefers

    # Extension point for preference-specific attributes
    properties: Dict[str, Any] = {}


@dataclass
class Consumer:
    """Patient or service consumer."""
    id: Annotated[UUID, PlanningId]
    name: str

    # Core consumer data
    location: Optional[Any] = None  # Location entity will be imported
    care_episode_id: Optional[str] = None

    # All consumer preferences consolidated into structured object
    consumer_preferences: ConsumerPreferences = field(default_factory=ConsumerPreferences)

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = field(default_factory=dict) 