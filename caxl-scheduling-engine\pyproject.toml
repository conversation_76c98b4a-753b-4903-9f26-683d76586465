[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "caxl-scheduling-engine"
version = "0.1.0"
description = "Scheduling Engine for CareAxl"
authors = [
    {name = "CareAxl Team", email = "<EMAIL>"},
]

dependencies = [
    "fastapi>=0.110.0",
    "python-dotenv>=1.0.1",
    "dependency-injector>=4.46.0",
    "uvicorn>=0.33.0",
    "pydantic>=2.7.2",
    "pydantic-settings>=2.9.1",
    "httpx==0.25.0",
    "timefold>=1.23.0b0",
    "loguru>=0.7.0",
    "pyyaml>=6.0",
    "schedule>=1.2.0",
    "python-multipart>=0.0.9",
    "email-validator>=2.1.0.post1",
]
requires-python = ">=3.11"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
dev = [
    "black==24.10.0",
    "ruff==0.1.10",
    "mypy==1.15.0",
    "pytest==8.3.5",
    "pytest-asyncio==0.26.0",
    "pytest-cov==4.1.0",
    "pre-commit>=3.7.1",
    "bandit==1.7.5",
    "pytest-mock==3.14.0"
]

[project.scripts]
assign-appointments = "src.application.usecases.assign_appointments:main"
day-plan = "src.application.usecases.day_plan:main"
scheduler = "src.scheduler:main"
api-server = "src.main:main"

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=src --cov-report=term-missing"

[tool.black]
line-length = 100
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
strict = true
ignore_missing_imports = true
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.ruff]
line-length = 100
target-version = "py311"
select = ["E", "F", "B", "I", "N", "UP", "PL", "RUF"]
ignore = ["E501", "B008", "PLR0913", "UP007"]
extend-exclude = ["tests"]

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.coverage.run]
source = ["src"]
omit = ["tests/*", "**/__init__.py"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if __name__ == .__main__.:",
    "raise NotImplementedError",
    "if TYPE_CHECKING:",
    "pass",
    "@abstractmethod",
]

[tool.ruff.per-file-ignores]
"tests/*" = ["PLR2004", "S101"] 