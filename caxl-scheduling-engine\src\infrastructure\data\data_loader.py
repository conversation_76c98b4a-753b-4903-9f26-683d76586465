"""
Data loader for healthcare scheduling optimization.

This module provides stub data for API consumption and demo purposes.
In a real implementation, this would load data from external services or databases.
"""

import yaml
from datetime import date, datetime, time
from typing import Dict, List, Any, Optional
from uuid import uuid4, UUID

from loguru import logger

from src.domain.entities.appointment import (
    AppointmentData, ScheduledAppointment, AppointmentTiming, AppointmentRelationships, AppointmentPinning
)
from src.domain.entities.provider import (
    Provider, ProviderAvailability, ProviderCapacity, ProviderPreferences, ShiftPattern
)
from src.domain.entities.consumer import Consumer, ConsumerPreferences
from src.domain.value_objects.location import Location

# Import scenario loader
from .scenario_loader import load_scenario, get_available_scenarios, get_scenario_info


def create_demo_data(scenario_name: Optional[str] = None) -> Dict[str, List[Any]]:
    """
    Create demo data for testing and development.
    
    Args:
        scenario_name: Optional scenario name to load. If None, uses default demo data.
        
    Returns:
        Dictionary containing providers, consumers, and appointments
    """
    if scenario_name:
        try:
            logger.info(f"Loading scenario: {scenario_name}")
            return load_scenario(scenario_name)
        except Exception as e:
            logger.warning(f"Failed to load scenario '{scenario_name}': {e}")
            logger.info("Falling back to default demo data")
    
    logger.info("Creating default demo data...")
    
    # Create locations
    locations = _create_demo_locations()
    
    # Create providers
    providers = _create_demo_providers(locations)
    
    # Create consumers
    consumers = _create_demo_consumers(locations)
    
    # Create appointments
    appointments = _create_demo_appointments(consumers)
    
    logger.info(f"Demo data created: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")
    
    return {
        "providers": providers,
        "consumers": consumers,
        "appointments": appointments
    }


def create_demo_scheduled_appointments(target_date: date, scenario_name: Optional[str] = None) -> List[ScheduledAppointment]:
    """
    Create demo scheduled appointments for a specific date.
    
    Args:
        target_date: Date to create scheduled appointments for
        scenario_name: Optional scenario name to load
        
    Returns:
        List of scheduled appointments
    """
    logger.info(f"Creating demo scheduled appointments for {target_date}")
    
    # Get demo data (with scenario support)
    demo_data = create_demo_data(scenario_name)
    providers = demo_data["providers"]
    consumers = demo_data["consumers"]
    appointments = demo_data["appointments"]
    
    # Create scheduled appointments (appointments that have been assigned provider and date)
    scheduled_appointments = []
    
    for i, appointment in enumerate(appointments[:5]):  # Limit to 5 for demo
        if i < len(providers):
            provider = providers[i]
            scheduled_appointment = ScheduledAppointment(
                id=f"scheduled_{appointment.id}",
                appointment_data=appointment,
                provider=provider,
                assigned_date=target_date,
                assigned_time=None  # Will be assigned by DayPlan
            )
            scheduled_appointments.append(scheduled_appointment)
    
    logger.info(f"Created {len(scheduled_appointments)} scheduled appointments for {target_date}")
    return scheduled_appointments


def get_available_scenarios() -> List[str]:
    """Get list of available scenarios."""
    return get_available_scenarios()


def get_scenario_info(scenario_name: str) -> Dict[str, Any]:
    """Get information about a specific scenario."""
    return get_scenario_info(scenario_name)


def _create_demo_locations() -> List[Location]:
    """Create demo locations."""
    return [
        Location(
            latitude=40.7128,
            longitude=-74.0060,
            address="123 Main St",
            city="New York",
            state="NY",
            country="USA",
            zip_code="10001"
        ),
        Location(
            latitude=40.7589,
            longitude=-73.9851,
            address="456 Broadway",
            city="New York",
            state="NY",
            country="USA",
            zip_code="10013"
        ),
        Location(
            latitude=40.7505,
            longitude=-73.9934,
            address="789 5th Ave",
            city="New York",
            state="NY",
            country="USA",
            zip_code="10022"
        )
    ]


def _create_demo_providers(locations: List[Location]) -> List[Provider]:
    """Create demo providers."""
    providers = []
    
    # Provider 1: Physical Therapist
    provider1 = Provider(
        id=uuid4(),
        name="Dr. Sarah Johnson",
        home_location=locations[0],
        languages=["English", "Spanish"],
        transportation="car",
        role="PT",
        skills=["physical_therapy", "orthopedic", "geriatric"],
        availability=_create_provider_availability(),
        capacity=ProviderCapacity(
            max_allocated_task_points_in_day=30,
            max_tasks_count_in_day=8,
            max_hours_per_day=8
        ),
        provider_preferences=ProviderPreferences(
            preferred_task_types=["physical_therapy"],
            blacklisted_task_types=["skilled_nursing"]
        )
    )
    providers.append(provider1)
    
    # Provider 2: Skilled Nurse
    provider2 = Provider(
        id=uuid4(),
        name="Nurse Maria Rodriguez",
        home_location=locations[1],
        languages=["English", "Spanish"],
        transportation="car",
        role="RN",
        skills=["skilled_nursing", "wound_care", "medication_management"],
        availability=_create_provider_availability(),
        capacity=ProviderCapacity(
            max_allocated_task_points_in_day=25,
            max_tasks_count_in_day=6,
            max_hours_per_day=8
        ),
        provider_preferences=ProviderPreferences(
            preferred_task_types=["skilled_nursing"],
            blacklisted_task_types=["physical_therapy"]
        )
    )
    providers.append(provider2)
    
    # Provider 3: Home Health Aide
    provider3 = Provider(
        id=uuid4(),
        name="Aide James Wilson",
        home_location=locations[2],
        languages=["English"],
        transportation="public_transit",
        role="CNA",
        skills=["personal_care", "companionship", "light_housekeeping"],
        availability=_create_provider_availability(),
        capacity=ProviderCapacity(
            max_allocated_task_points_in_day=20,
            max_tasks_count_in_day=5,
            max_hours_per_day=8
        ),
        provider_preferences=ProviderPreferences(
            preferred_task_types=["personal_care"],
            blacklisted_task_types=[]
        )
    )
    providers.append(provider3)
    
    return providers


def _create_demo_consumers(locations: List[Location]) -> List[Consumer]:
    """Create demo consumers (patients)."""
    consumers = []
    
    # Consumer 1
    consumer1 = Consumer(
        id=uuid4(),
        name="John Smith",
        location=locations[0],
        care_episode_id="episode_001",
        consumer_preferences=ConsumerPreferences(
            preferred_days=["monday", "wednesday", "friday"],
            preferred_hours=(time(9, 0), time(17, 0)),
            language="English",
            preferred_providers=[]
        )
    )
    consumers.append(consumer1)
    
    # Consumer 2
    consumer2 = Consumer(
        id=uuid4(),
        name="Maria Garcia",
        location=locations[1],
        care_episode_id="episode_002",
        consumer_preferences=ConsumerPreferences(
            preferred_days=["tuesday", "thursday"],
            preferred_hours=(time(10, 0), time(16, 0)),
            language="Spanish",
            preferred_providers=[]
        )
    )
    consumers.append(consumer2)
    
    # Consumer 3
    consumer3 = Consumer(
        id=uuid4(),
        name="Robert Johnson",
        location=locations[2],
        care_episode_id="episode_003",
        consumer_preferences=ConsumerPreferences(
            preferred_days=["monday", "friday"],
            preferred_hours=(time(8, 0), time(18, 0)),
            language="English",
            preferred_providers=[]
        )
    )
    consumers.append(consumer3)
    
    return consumers


def _create_demo_appointments(consumers: List[Consumer]) -> List[AppointmentData]:
    """Create demo appointments."""
    appointments = []
    
    # Physical therapy appointments
    for i, consumer in enumerate(consumers):
        appointment = AppointmentData(
            id=uuid4(),
            consumer_id=consumer.id,
            appointment_date=date.today(),
            required_skills=["physical_therapy"],
            duration_min=60,
            urgent=False,
            location=consumer.location,
            priority="normal",
            task_points=8,
            required_role="PT",
            timing=AppointmentTiming(
                is_timed_visit=False,
                time_flexibility_minutes=30
            ),
            relationships=AppointmentRelationships(
                care_episode_id=consumer.care_episode_id
            ),
            pinning=AppointmentPinning()
        )
        appointments.append(appointment)
    
    # Skilled nursing appointments
    for i, consumer in enumerate(consumers):
        appointment = AppointmentData(
            id=uuid4(),
            consumer_id=consumer.id,
            appointment_date=date.today() + date.resolution,
            required_skills=["skilled_nursing"],
            duration_min=45,
            urgent=False,
            location=consumer.location,
            priority="normal",
            task_points=6,
            required_role="RN",
            timing=AppointmentTiming(
                is_timed_visit=True,
                preferred_time=time(10, 0),
                time_flexibility_minutes=15
            ),
            relationships=AppointmentRelationships(
                care_episode_id=consumer.care_episode_id
            ),
            pinning=AppointmentPinning()
        )
        appointments.append(appointment)
    
    return appointments


def _create_provider_availability() -> ProviderAvailability:
    """Create demo provider availability."""
    primary_shift = ShiftPattern(
        shift_name="day_shift",
        shift_start=time(8, 0),
        shift_end=time(17, 0),
        crosses_midnight=False,
        shift_days=["monday", "tuesday", "wednesday", "thursday", "friday"]
    )
    
    return ProviderAvailability(
        primary_shift=primary_shift,
        working_days=["monday", "tuesday", "wednesday", "thursday", "friday"],
        break_periods=[(time(12, 0), time(13, 0))],
        max_hours_per_day=8,
        max_hours_per_week=40,
        min_gap_between_assignments=30
    )


def load_appointments_from_yaml(file_path: str) -> List[AppointmentData]:
    """Load appointments from YAML file (stub implementation)."""
    logger.info(f"Loading appointments from {file_path}")
    
    # This would normally load from a YAML file
    # For now, return demo data
    demo_data = create_demo_data()
    return demo_data["appointments"]


def load_providers_from_yaml(file_path: str) -> List[Provider]:
    """Load providers from YAML file (stub implementation)."""
    logger.info(f"Loading providers from {file_path}")
    
    # This would normally load from a YAML file
    # For now, return demo data
    demo_data = create_demo_data()
    return demo_data["providers"]


def load_consumers_from_yaml(file_path: str) -> List[Consumer]:
    """Load consumers from YAML file (stub implementation)."""
    logger.info(f"Loading consumers from {file_path}")
    
    # This would normally load from a YAML file
    # For now, return demo data
    demo_data = create_demo_data()
    return demo_data["consumers"] 