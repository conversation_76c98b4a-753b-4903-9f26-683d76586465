## Description of the change

> Description here

## Checklist

### Development

- [ ] The changes have been covered with tests, if necessary.
- [ ] You have a green build, with the exception of the flaky tests.
- [ ] UI and JS files are fully tested, the user interface works for all modules affected by your changes (e.g., solve and analyze buttons).
- [ ] The network calls work for all modules affected by your changes (e.g., solving a problem).
- [ ] The console messages are validated for all modules affected by your changes.

### Code Review

- [ ] This pull request includes an explanatory title and description.
- [ ] The GitHub issue is linked.
- [ ] At least one other engineer has approved the changes.
- [ ] After PR is merged, inform the reporter.