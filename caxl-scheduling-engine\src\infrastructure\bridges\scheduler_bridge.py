"""
Scheduler bridge for constraint definitions and optimization logic.
"""

from datetime import time
from typing import List, Dict, Any
from timefold.solver.score import HardSoftScore

from ...domain.entities.appointment import AppointmentSchedule, AppointmentAssignment, DaySchedule, TimeSlotAssignment


def define_constraints(score_director) -> HardSoftScore:
    """
    Define constraints for appointment assignment optimization.
    
    This function defines the scoring rules for the Timefold solver.
    """
    hard_score = 0
    soft_score = 0
    
    # Get all assignments
    assignments = score_director.get_working_entity_list(AppointmentAssignment)
    
    # Hard constraints
    for assignment in assignments:
        if assignment.provider and assignment.assigned_date:
            # Check provider capacity
            if _is_provider_over_capacity(assignment, assignments):
                hard_score -= 1
            
            # Check provider availability
            if not _is_provider_available(assignment):
                hard_score -= 1
            
            # Check required skills
            if not _has_required_skills(assignment):
                hard_score -= 1
    
    # Soft constraints
    for assignment in assignments:
        if assignment.provider and assignment.assigned_date:
            # Prefer geographic proximity
            if _is_geographically_close(assignment):
                soft_score += 1
            
            # Prefer continuity of care
            if _maintains_continuity(assignment, assignments):
                soft_score += 1
            
            # Prefer workload balance
            if _is_workload_balanced(assignment, assignments):
                soft_score += 1
    
    return HardSoftScore(hard_score, soft_score)


def define_day_constraints(score_director) -> HardSoftScore:
    """
    Define constraints for day planning optimization.
    
    This function defines the scoring rules for time slot assignment.
    """
    hard_score = 0
    soft_score = 0
    
    # Get all time assignments
    time_assignments = score_director.get_working_entity_list(TimeSlotAssignment)
    
    # Hard constraints
    for assignment in time_assignments:
        if assignment.time_slot:
            # Check for double booking
            if _is_double_booked(assignment, time_assignments):
                hard_score -= 1
            
            # Check provider availability at time
            if not _is_provider_available_at_time(assignment):
                hard_score -= 1
            
            # Check appointment timing constraints
            if not _satisfies_timing_constraints(assignment):
                hard_score -= 1
    
    # Soft constraints
    for assignment in time_assignments:
        if assignment.time_slot:
            # Prefer preferred hours
            if _is_preferred_hours(assignment):
                soft_score += 1
            
            # Prefer efficient routing
            if _is_efficient_routing(assignment, time_assignments):
                soft_score += 1
    
    return HardSoftScore(hard_score, soft_score)


def _is_provider_over_capacity(assignment: AppointmentAssignment, all_assignments: List[AppointmentAssignment]) -> bool:
    """Check if provider is over capacity."""
    if not assignment.provider or not assignment.assigned_date:
        return False
    
    # Count assignments for this provider on this date
    provider_assignments = [
        a for a in all_assignments 
        if a.provider == assignment.provider and a.assigned_date == assignment.assigned_date
    ]
    
    # Check against provider capacity
    if hasattr(assignment.provider, 'capacity'):
        max_tasks = assignment.provider.capacity.max_tasks_count_in_day
        return len(provider_assignments) > max_tasks
    
    return False


def _is_provider_available(assignment: AppointmentAssignment) -> bool:
    """Check if provider is available on the assigned date."""
    if not assignment.provider or not assignment.assigned_date:
        return False
    
    if hasattr(assignment.provider, 'availability'):
        return assignment.provider.availability.get_shift_hours(assignment.assigned_date) is not None
    
    return True


def _has_required_skills(assignment: AppointmentAssignment) -> bool:
    """Check if provider has required skills for the appointment."""
    if not assignment.provider:
        return False
    
    required_skills = assignment.appointment_data.required_skills
    provider_skills = assignment.provider.skills
    
    return any(skill in provider_skills for skill in required_skills)


def _is_geographically_close(assignment: AppointmentAssignment) -> bool:
    """Check if appointment is geographically close to provider."""
    if not assignment.provider or not assignment.appointment_data.location:
        return False
    
    # Simple distance calculation (in a real implementation, this would use proper geospatial calculations)
    provider_location = assignment.provider.home_location
    appointment_location = assignment.appointment_data.location
    
    if not provider_location or not appointment_location:
        return False
    
    # Calculate simple distance (this is a simplified version)
    lat_diff = abs(provider_location.latitude - appointment_location.latitude)
    lon_diff = abs(provider_location.longitude - appointment_location.longitude)
    
    # Rough conversion to miles (1 degree ≈ 69 miles)
    distance_miles = ((lat_diff ** 2 + lon_diff ** 2) ** 0.5) * 69
    
    return distance_miles <= 25  # Within 25 miles


def _maintains_continuity(assignment: AppointmentAssignment, all_assignments: List[AppointmentAssignment]) -> bool:
    """Check if assignment maintains continuity of care."""
    if not assignment.provider:
        return False
    
    # Check if this provider has seen this patient before
    patient_id = assignment.appointment_data.consumer_id
    provider_assignments = [
        a for a in all_assignments 
        if a.provider == assignment.provider and a.appointment_data.consumer_id == patient_id
    ]
    
    return len(provider_assignments) > 1


def _is_workload_balanced(assignment: AppointmentAssignment, all_assignments: List[AppointmentAssignment]) -> bool:
    """Check if assignment contributes to workload balance."""
    if not assignment.provider or not assignment.assigned_date:
        return False
    
    # Count assignments for this provider on this date
    provider_assignments = [
        a for a in all_assignments 
        if a.provider == assignment.provider and a.assigned_date == assignment.assigned_date
    ]
    
    # Count assignments for all providers on this date
    all_provider_assignments = [
        a for a in all_assignments 
        if a.assigned_date == assignment.assigned_date
    ]
    
    if not all_provider_assignments:
        return True
    
    # Check if this provider's workload is close to average
    avg_workload = len(all_provider_assignments) / len(set(a.provider for a in all_provider_assignments))
    provider_workload = len(provider_assignments)
    
    return abs(provider_workload - avg_workload) <= 1


def _is_double_booked(assignment: TimeSlotAssignment, all_assignments: List[TimeSlotAssignment]) -> bool:
    """Check if provider is double booked at this time."""
    if not assignment.time_slot:
        return False
    
    provider = assignment.scheduled_appointment.provider
    time_slot = assignment.time_slot
    
    # Check other assignments for the same provider at the same time
    for other_assignment in all_assignments:
        if (other_assignment != assignment and 
            other_assignment.scheduled_appointment.provider == provider and
            other_assignment.time_slot == time_slot):
            return True
    
    return False


def _is_provider_available_at_time(assignment: TimeSlotAssignment) -> bool:
    """Check if provider is available at the assigned time."""
    if not assignment.time_slot:
        return False
    
    provider = assignment.scheduled_appointment.provider
    appointment_date = assignment.scheduled_appointment.assigned_date
    time_slot = assignment.time_slot
    
    if hasattr(provider, 'availability'):
        shift_hours = provider.availability.get_shift_hours(appointment_date)
        if not shift_hours:
            return False
        
        start_time, end_time = shift_hours
        return start_time <= time_slot <= end_time
    
    return True


def _satisfies_timing_constraints(assignment: TimeSlotAssignment) -> bool:
    """Check if assignment satisfies timing constraints."""
    if not assignment.time_slot:
        return False
    
    appointment = assignment.scheduled_appointment.appointment_data
    
    # Check if it's a timed visit
    if appointment.timing.is_timed_visit and appointment.timing.preferred_time:
        # Check if within flexibility window
        time_diff = abs((assignment.time_slot.hour * 60 + assignment.time_slot.minute) - 
                       (appointment.timing.preferred_time.hour * 60 + appointment.timing.preferred_time.minute))
        return time_diff <= appointment.timing.time_flexibility_minutes
    
    return True


def _is_preferred_hours(assignment: TimeSlotAssignment) -> bool:
    """Check if assignment is within preferred hours."""
    if not assignment.time_slot:
        return False
    
    appointment = assignment.scheduled_appointment.appointment_data
    consumer = appointment.consumer_id  # This would need to be resolved to actual consumer
    
    # For now, assume preferred hours are 9 AM to 5 PM
    preferred_start = 9 * 60  # 9 AM in minutes
    preferred_end = 17 * 60   # 5 PM in minutes
    
    time_minutes = assignment.time_slot.hour * 60 + assignment.time_slot.minute
    
    return preferred_start <= time_minutes <= preferred_end


def _is_efficient_routing(assignment: TimeSlotAssignment, all_assignments: List[TimeSlotAssignment]) -> bool:
    """Check if assignment contributes to efficient routing."""
    if not assignment.time_slot:
        return False
    
    # This is a simplified routing check
    # In a real implementation, this would calculate actual travel times and optimize routes
    
    provider = assignment.scheduled_appointment.provider
    provider_assignments = [
        a for a in all_assignments 
        if a.scheduled_appointment.provider == provider and a.time_slot
    ]
    
    # Sort by time, filtering out None values
    sorted_assignments = sorted(provider_assignments, key=lambda a: a.time_slot or time(0, 0))
    
    # Check if assignments are reasonably spaced
    for i in range(len(sorted_assignments) - 1):
        current_time = sorted_assignments[i].time_slot
        next_time = sorted_assignments[i + 1].time_slot
        
        if current_time and next_time:
            time_diff = (next_time.hour * 60 + next_time.minute) - (current_time.hour * 60 + current_time.minute)
            
            # Require at least 30 minutes between appointments
            if time_diff < 30:
                return False
    
    return True 