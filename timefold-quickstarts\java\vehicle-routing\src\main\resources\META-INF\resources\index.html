<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Vehicle Routing - Timefold Solver on Quarkus</title>
  <link rel="stylesheet" href="/webjars/bootstrap/css/bootstrap.min.css">
  <link rel="stylesheet" href="/webjars/leaflet/leaflet.css">
  <link rel="stylesheet" href="/webjars/font-awesome/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vis-timeline@7.7.2/styles/vis-timeline-graph2d.min.css"
        integrity="sha256-svzNasPg1yR5gvEaRei2jg+n4Pc3sVyMUWeS6xRAh6U=" crossorigin="anonymous">
  <link rel="stylesheet" href="/webjars/timefold/css/timefold-webui.css"/>
  <link rel="icon" href="/webjars/timefold/img/timefold-favicon.svg" type="image/svg+xml">
</head>
<body>

<header id="timefold-auto-header">
  <!-- Filled in by app.js -->
</header>
<div class="tab-content">
  <div id="demo" class="tab-pane fade show active container-fluid">
    <div class="sticky-top d-flex justify-content-center align-items-center">
      <div id="notificationPanel" style="position: absolute; top: .5rem;"></div>
    </div>
    <h1>Vehicle routing with capacity and time windows</h1>
    <p>Generate optimal route plan of a vehicle fleet with limited vehicle capacity and time windows. New visits may be added by clicking anywhere inside the map.</p>
    <div class="container-fluid mb-2">
      <div class="row justify-content-start">
        <div class="col-9">
          <ul class="nav nav-pills col" role="tablist">
            <li class="nav-item" role="presentation">
              <button class="nav-link active" id="mapTab" data-bs-toggle="tab" data-bs-target="#mapPanel"
                      type="button"
                      role="tab" aria-controls="mapPanel" aria-selected="false">Map
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="byVehicleTab" data-bs-toggle="tab" data-bs-target="#byVehiclePanel"
                      type="button" role="tab" aria-controls="byVehiclePanel" aria-selected="true">By vehicle
              </button>
            </li>
            <li class="nav-item" role="presentation">
              <button class="nav-link" id="byVisitTab" data-bs-toggle="tab" data-bs-target="#byVisitPanel"
                      type="button" role="tab" aria-controls="byVisitPanel" aria-selected="false">By visit
              </button>
            </li>
          </ul>
        </div>
        <div class="col-3">
          <button id="solveButton" type="button" class="btn btn-success">
            <i class="fas fa-play"></i> Solve
          </button>
          <button id="stopSolvingButton" type="button" class="btn btn-danger p-2">
            <i class="fas fa-stop"></i> Stop solving
          </button>
          <span id="score" class="score ms-2 align-middle fw-bold">Score: ?</span>
          <button id="analyzeButton" type="button" class="ms-2 btn btn-secondary">
            <span class="fas fa-question"></span>
          </button>
        </div>
      </div>
    </div>

    <div class="tab-content">

      <div class="tab-pane fade show active" id="mapPanel" role="tabpanel" aria-labelledby="mapTab">
        <div class="row">
          <div class="col-7 col-lg-8 col-xl-9">
            <div id="map" style="width: 100%; height: 100vh;"></div>
          </div>
          <div class="col-5 col-lg-4 col-xl-3" style="height: 100vh; overflow-y: scroll;">
            <div class="row pt-2 row-cols-1">
              <div class="col">
                <h5>
                  Solution summary
                </h5>
                <table class="table">
                  <tr>
                    <td>Total driving time:</td>
                    <td><span id="drivingTime">unknown</span></td>
                  </tr>
                </table>
              </div>
              <div class="col">
                <h5>Vehicles</h5>
                <table class="table-sm w-100">
                  <thead>
                  <tr>
                    <th class="col-1"></th>
                    <th class="col-3">Name</th>
                    <th class="col-3">
                      Load
                      <i class="fas fa-info-circle" data-bs-toggle="tooltip" data-bs-placement="top"
                         data-html="true"
                         title="Vehicle load is displayed as: total cargo / vehicle capacity."></i>
                    </th>
                    <th class="col-3">Driving time</th>
                  </tr>
                  </thead>
                  <tbody id="vehicles"></tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="tab-pane fade" id="byVehiclePanel" role="tabpanel" aria-labelledby="byVehicleTab">
      </div>
      <div class="tab-pane fade" id="byVisitPanel" role="tabpanel" aria-labelledby="byVisitTab">
      </div>
    </div>
  </div>

  <div id="rest" class="tab-pane fade container-fluid">
    <h1>REST API Guide</h1>

    <h2>Vehicle routing with vehicle capacity and time windows - integration via cURL</h2>

    <h3>1. Download demo data</h3>
    <pre>
              <button class="btn btn-outline-dark btn-sm float-end"
                      onclick="copyTextToClipboard('curl1')">Copy</button>
              <code id="curl1">curl -X GET -H 'Accept:application/json' http://localhost:8080/demo-data/FIRENZE -o sample.json</code>
    </pre>

    <h3>2. Post the sample data for solving</h3>
    <p>The POST operation returns a <code>jobId</code> that should be used in subsequent commands.</p>
    <pre>
              <button class="btn btn-outline-dark btn-sm float-end"
                      onclick="copyTextToClipboard('curl2')">Copy</button>
              <code id="curl2">curl -X POST -H 'Content-Type:application/json' http://localhost:8080/route-plans -<EMAIL></code>
    </pre>

    <h3>3. Get the current status and score</h3>
    <pre>
              <button class="btn btn-outline-dark btn-sm float-end"
                      onclick="copyTextToClipboard('curl3')">Copy</button>
              <code id="curl3">curl -X GET -H 'Accept:application/json' http://localhost:8080/route-plans/{jobId}/status</code>
    </pre>

    <h3>4. Get the complete route plan</h3>
    <pre>
              <button class="btn btn-outline-dark btn-sm float-end"
                      onclick="copyTextToClipboard('curl4')">Copy</button>
              <code id="curl4">curl -X GET -H 'Accept:application/json' http://localhost:8080/route-plans/{jobId}</code>
    </pre>

    <h3>5. Terminate solving early</h3>
    <pre>
              <button class="btn btn-outline-dark btn-sm float-end"
                      onclick="copyTextToClipboard('curl5')">Copy</button>
              <code id="curl5">curl -X DELETE -H 'Accept:application/json' http://localhost:8080/route-plans/{jobId}</code>
    </pre>
  </div>

  <div id="openapi" class="tab-pane fade container-fluid">
    <h1>REST API Reference</h1>
    <div class="ratio ratio-1x1">
      <!-- "scrolling" attribute is obsolete, but e.g. Chrome does not support "overflow:hidden" -->
      <iframe src="/q/swagger-ui" style="overflow:hidden;" scrolling="no"></iframe>
    </div>
  </div>
</div>
<div class="modal fadebd-example-modal-lg" id="scoreAnalysisModal" tabindex="-1" aria-labelledby="scoreAnalysisModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="scoreAnalysisModalLabel">Score analysis <span id="scoreAnalysisScoreLabel"></span></h1>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="scoreAnalysisModalContent">
        <!-- Filled in by app.js -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>
<form id='visitForm' class='needs-validation' novalidate>
    <div class="modal fadebd-example-modal-lg" id="newVisitModal" tabindex="-1"
         aria-labelledby="newVisitModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="newVisitModalLabel">Add New Visit</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body" id="newVisitModalContent">
                    <!-- Filled in by app.js -->
                </div>
                <div class="modal-footer" id="newVisitModalFooter">
                </div>
            </div>
        </div>
    </div>
</form>
<footer id="timefold-auto-footer"></footer>

<script src="/webjars/flatpickr/dist/flatpickr.min.js"></script>
<link rel="stylesheet" href="/webjars/flatpickr/dist/flatpickr.min.css">
<script src="/webjars/leaflet/leaflet.js"></script>
<script src="/webjars/jquery/jquery.min.js"></script>
<script src="/webjars/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="/webjars/js-joda/dist/js-joda.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vis-timeline@7.7.2/standalone/umd/vis-timeline-graph2d.min.js"
        integrity="sha256-Jy2+UO7rZ2Dgik50z3XrrNpnc5+2PAx9MhL2CicodME=" crossorigin="anonymous"></script>
<script src="/webjars/timefold/js/timefold-webui.js"></script>
<script src="/score-analysis.js"></script>
<script src="/recommended-fit.js"></script>
<script src="/app.js"></script>
</body>
</html>
