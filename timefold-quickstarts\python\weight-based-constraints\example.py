#!/usr/bin/env python3
"""
Weight-Based Constraints Example

This standalone script demonstrates weight-based constraints in Timefold.
Run this script directly to see the example in action.

Usage:
    python example.py
"""

import sys
import os

# Add the src directory to the path so we can import our modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from weight_based_constraints import main, run_small_example


if __name__ == "__main__":
    print("Weight-Based Constraints Example")
    print("=" * 50)
    print()
    print("Choose an example to run:")
    print("1. Small example (quick test)")
    print("2. Full example (comprehensive demo)")
    print()
    
    try:
        choice = input("Enter your choice (1 or 2): ").strip()
        
        if choice == "1":
            print("\nRunning small example...")
            run_small_example()
        elif choice == "2":
            print("\nRunning full example...")
            main()
        else:
            print("Invalid choice. Running small example by default.")
            run_small_example()
            
    except KeyboardInterrupt:
        print("\n\nExample interrupted by user.")
    except Exception as e:
        print(f"\nError running example: {e}")
        print("\nTrying to run small example instead...")
        try:
            run_small_example()
        except Exception as e2:
            print(f"Error running small example: {e2}")
            print("\nPlease check that Timefold is properly installed:")
            print("pip install timefold==1.23.0b0") 