name: <PERSON>ven

on:
  push:
    branches: [stable, development, '*.x']
    paths:
      - 'pom.xml'
      - 'java/**/*'
      - 'kotlin/**/*'
      - '.github/**/*.yml'
  pull_request:
    branches: [stable, development, '*.x']
    paths:
      - 'pom.xml'
      - 'java/**/*'
      - 'kotlin/**/*'
      - '.github/**/*.yml'

jobs:
  build:
    concurrency:
      group: pull_request-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.os }}-${{ matrix.java-version }}
      cancel-in-progress: true
    runs-on: ${{matrix.os}}
    strategy:
      matrix:
        os: [ubuntu-latest, ubuntu-24.04-arm, windows-latest, macOS-latest]
        java-version: [ 17, 21, 24 ] #Latest two LTS + latest non-LTS.
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: './timefold-quickstarts'

      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        if: github.head_ref # Only true if this is a PR.
        id: checkout-solver-pr
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: ${{ steps.checkout-solver-pr.outcome != 'success' }}
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build and test
      - name: "Setup Java and Maven"
        uses: actions/setup-java@v3
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'temurin'
          cache: 'maven'
      - name: Quickly build timefold-solver
        working-directory: ./timefold-solver
        run: mvn -B -Dquickly -DskipTests clean install
      - name: Build and test timefold-quickstarts
        working-directory: ./timefold-quickstarts
        run: mvn -B verify
