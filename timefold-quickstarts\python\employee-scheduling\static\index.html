<html lang="en">
<head>
    <meta content="text/html; charset=UTF-8" http-equiv="Content-Type">
    <meta content="width=device-width, initial-scale=1" name="viewport">
    <title>Employee scheduling - Timefold Solver for Python</title>

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/vis-timeline@7.7.2/styles/vis-timeline-graph2d.min.css"
          integrity="sha256-svzNasPg1yR5gvEaRei2jg+n4Pc3sVyMUWeS6xRAh6U=" crossorigin="anonymous">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/css/bootstrap.min.css"/>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"/>
  <link rel="stylesheet" href="/webjars/timefold/css/timefold-webui.css"/>
    <style>
        .vis-time-axis .vis-grid.vis-saturday,
        .vis-time-axis .vis-grid.vis-sunday {
            background: #D3D7CFFF;
        }
    </style>
    <link rel="icon" href="/webjars/timefold/img/timefold-favicon.svg" type="image/svg+xml">
</head>

<body>
<header id="timefold-auto-header">
    <!-- Filled in by app.js -->
</header>
<div class="tab-content">
    <div id="demo" class="tab-pane fade show active container-fluid">
        <div class="sticky-top d-flex justify-content-center align-items-center" aria-live="polite"
             aria-atomic="true">
            <div id="notificationPanel" style="position: absolute; top: .5rem;"></div>
        </div>
        <h1>Employee scheduling solver</h1>
        <p>Generate the optimal schedule for your employees.</p>

        <div class="mb-4">
            <button id="solveButton" type="button" class="btn btn-success">
                <span class="fas fa-play"></span> Solve
            </button>
            <button id="stopSolvingButton" type="button" class="btn btn-danger">
                <span class="fas fa-stop"></span> Stop solving
            </button>
            <span id="unassignedShifts" class="ms-2 align-middle fw-bold"></span>
            <span id="score" class="score ms-2 align-middle fw-bold">Score: ?</span>

            <div class="float-end">
                <ul class="nav nav-pills" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="byLocationTab" data-bs-toggle="tab"
                                data-bs-target="#byLocationPanel" type="button" role="tab"
                                aria-controls="byLocationPanel" aria-selected="true">By location
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="byEmployeeTab" data-bs-toggle="tab"
                                data-bs-target="#byEmployeePanel" type="button" role="tab"
                                aria-controls="byEmployeePanel" aria-selected="false">By employee
                        </button>
                    </li>
                </ul>
            </div>
        </div>
        <div class="mb-4 tab-content">
            <div class="tab-pane fade show active" id="byLocationPanel" role="tabpanel"
                 aria-labelledby="byLocationTab">
                <div id="locationVisualization"></div>
            </div>
            <div class="tab-pane fade" id="byEmployeePanel" role="tabpanel" aria-labelledby="byEmployeeTab">
                <div id="employeeVisualization"></div>
            </div>
        </div>
    </div>

    <div id="rest" class="tab-pane fade  container-fluid">
        <h1>REST API Guide</h1>

        <h2>Employee Scheduling solver integration via cURL</h2>

        <h3>1. Download demo data</h3>
        <pre>
            <button class="btn btn-outline-dark btn-sm float-end"
                    onclick="copyTextToClipboard('curl1')">Copy</button>
            <code id="curl1">curl -X GET -H 'Accept:application/json' http://localhost:8080/demo-data/SMALL -o sample.json</code>
    </pre>

        <h3>2. Post the sample data for solving</h3>
        <p>The POST operation returns a <code>jobId</code> that should be used in subsequent commands.</p>
        <pre>
            <button class="btn btn-outline-dark btn-sm float-end"
                    onclick="copyTextToClipboard('curl2')">Copy</button>
            <code id="curl2">curl -X POST -H 'Content-Type:application/json' http://localhost:8080/schedules -<EMAIL></code>
    </pre>

        <h3>3. Get the current status and score</h3>
        <pre>
            <button class="btn btn-outline-dark btn-sm float-end"
                    onclick="copyTextToClipboard('curl3')">Copy</button>
            <code id="curl3">curl -X GET -H 'Accept:application/json' http://localhost:8080/schedules/{jobId}/status</code>
    </pre>

        <h3>4. Get the complete solution</h3>
        <pre>
            <button class="btn btn-outline-dark btn-sm float-end"
                    onclick="copyTextToClipboard('curl4')">Copy</button>
            <code id="curl4">curl -X GET -H 'Accept:application/json' http://localhost:8080/schedules/{jobId}</code>
    </pre>

        <h3>5. Terminate solving early</h3>
        <pre>
            <button class="btn btn-outline-dark btn-sm float-end"
                    onclick="copyTextToClipboard('curl5')">Copy</button>
            <code id="curl5">curl -X DELETE -H 'Accept:application/json' http://localhost:8080/schedules/{id}</code>
    </pre>
    </div>

    <div id="openapi" class="tab-pane fade container-fluid">
        <h1>REST API Reference</h1>
        <div class="ratio ratio-1x1">
            <!-- "scrolling" attribute is obsolete, but e.g. Chrome does not support "overflow:hidden" -->
            <iframe src="/q/swagger-ui" style="overflow:hidden;" scrolling="no"></iframe>
        </div>
    </div>
</div>
<footer id="timefold-auto-footer"></footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.11.8/umd/popper.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.3/js/bootstrap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/js-joda/1.11.0/js-joda.min.js"></script>
<script src="/webjars/timefold/js/timefold-webui.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vis-timeline@7.7.2/standalone/umd/vis-timeline-graph2d.min.js"
        integrity="sha256-Jy2+UO7rZ2Dgik50z3XrrNpnc5+2PAx9MhL2CicodME=" crossorigin="anonymous"></script>
<script src="/app.js"></script>
</body>
</html>