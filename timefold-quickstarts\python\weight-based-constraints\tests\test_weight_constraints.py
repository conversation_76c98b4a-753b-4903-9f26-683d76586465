import pytest
from weight_based_constraints.domain import Container, Package, PackageAssignment, LoadingSolution
from weight_based_constraints.demo_data import create_small_demo_data, print_solution_summary


def test_domain_creation():
    """Test that domain objects can be created correctly."""
    container = Container("C1", "Test Container", max_weight=100.0, max_volume=10.0, priority=2)
    package = Package("P1", "Test Package", weight=50.0, volume=5.0, priority=1, fragility=1.5)
    assignment = PackageAssignment("PA1", package)
    
    assert container.id == "C1"
    assert container.max_weight == 100.0
    assert package.weight == 50.0
    assert assignment.package == package
    assert assignment.container is None


def test_solution_creation():
    """Test that LoadingSolution can be created with demo data."""
    solution = create_small_demo_data()
    
    assert solution.id == "small-demo-solution"
    assert len(solution.containers) == 3
    assert len(solution.package_assignments) == 5
    
    # Check that all packages are initially unassigned
    for assignment in solution.package_assignments:
        assert assignment.container is None


def test_container_utilization_calculation():
    """Test container utilization calculation methods."""
    # Create a container and some packages
    container = Container("C1", "Test Container", max_weight=100.0, max_volume=10.0)
    package1 = Package("P1", "Package 1", weight=30.0, volume=3.0)
    package2 = Package("P2", "Package 2", weight=20.0, volume=2.0)
    
    # Create assignments
    assignment1 = PackageAssignment("PA1", package1, container)
    assignment2 = PackageAssignment("PA2", package2, container)
    
    # Create solution
    solution = LoadingSolution(
        id="test-solution",
        containers=[container],
        package_assignments=[assignment1, assignment2]
    )
    
    # Test utilization calculations
    total_weight = solution.get_container_total_weight(container)
    total_volume = solution.get_container_total_volume(container)
    utilization_score = solution.get_container_utilization_score(container)
    
    assert total_weight == 50.0  # 30 + 20
    assert total_volume == 5.0   # 3 + 2
    assert utilization_score == 0.5  # (50/100 + 5/10) / 2 = 0.5


def test_weight_based_constraints_structure():
    """Test that the constraint structure is properly defined."""
    from weight_based_constraints.constraints import define_constraints
    
    # This should not raise an exception
    constraints = define_constraints(None)  # We can't actually call it without a factory
    
    # The function should exist and be callable
    assert callable(define_constraints)


def test_demo_data_variety():
    """Test that demo data includes various package types."""
    solution = create_small_demo_data()
    
    # Check that we have packages with different characteristics
    packages = [assignment.package for assignment in solution.package_assignments]
    
    # Check weight variety
    weights = [p.weight for p in packages]
    assert min(weights) < max(weights)  # Should have different weights
    
    # Check priority variety
    priorities = [p.priority for p in packages]
    assert min(priorities) < max(priorities)  # Should have different priorities
    
    # Check fragility variety
    fragilities = [p.fragility for p in packages]
    assert min(fragilities) < max(fragilities)  # Should have different fragility levels


def test_container_capacity_constraints():
    """Test that containers have reasonable capacity constraints."""
    solution = create_small_demo_data()
    
    for container in solution.containers:
        # Containers should have positive capacities
        assert container.max_weight > 0
        assert container.max_volume > 0
        
        # Containers should have reasonable priority levels
        assert container.priority >= 1
        assert container.priority <= 5


if __name__ == "__main__":
    # Run basic tests
    test_domain_creation()
    test_solution_creation()
    test_container_utilization_calculation()
    test_weight_based_constraints_structure()
    test_demo_data_variety()
    test_container_capacity_constraints()
    
    print("All basic tests passed!")
    
    # Create and display a small demo
    print("\n" + "="*50)
    print("DEMO DATA PREVIEW")
    print("="*50)
    solution = create_small_demo_data()
    print_solution_summary(solution) 