from timefold.solver.domain import (planning_entity, planning_solution, PlanningId, PlanningVariable,
                                    PlanningEntityCollectionProperty,
                                    ProblemFactCollectionProperty, ValueRangeProvider,
                                    PlanningScore)
from timefold.solver.score import HardSoftScore
from dataclasses import dataclass, field
from typing import Annotated, List, Optional


@dataclass
class Container:
    """A container that can hold packages with weight constraints."""
    id: str
    name: str
    max_weight: float  # Maximum weight capacity in kg
    max_volume: float  # Maximum volume capacity in cubic meters
    priority: int = 1  # Priority level (higher = more important)

    def __str__(self):
        return f'{self.name} (max: {self.max_weight}kg, {self.max_volume}m³)'


@dataclass
class Package:
    """A package that needs to be assigned to a container."""
    id: str
    name: str
    weight: float  # Weight in kg
    volume: float  # Volume in cubic meters
    priority: int = 1  # Priority level (higher = more important)
    fragility: float = 1.0  # Fragility factor (higher = more fragile)
    temperature_sensitive: bool = False  # Whether package requires temperature control

    def __str__(self):
        return f'{self.name} ({self.weight}kg, {self.volume}m³)'


@planning_entity
@dataclass
class PackageAssignment:
    """Planning entity representing the assignment of a package to a container."""
    id: Annotated[str, PlanningId]
    package: Package
    container: Annotated[Container | None, PlanningVariable] = field(default=None)

    def __str__(self):
        if self.container is None:
            return f'{self.package.name} -> unassigned'
        return f'{self.package.name} -> {self.container.name}'


@planning_solution
@dataclass
class LoadingSolution:
    """Solution representing the complete loading plan."""
    id: str
    containers: Annotated[List[Container],
                          ProblemFactCollectionProperty,
                          ValueRangeProvider]
    package_assignments: Annotated[List[PackageAssignment],
                                  PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)

    def get_container_assignments(self, container: Container) -> List[PackageAssignment]:
        """Get all packages assigned to a specific container."""
        return [assignment for assignment in self.package_assignments 
                if assignment.container == container]

    def get_container_total_weight(self, container: Container) -> float:
        """Calculate total weight of packages in a container."""
        assignments = self.get_container_assignments(container)
        return sum(assignment.package.weight for assignment in assignments)

    def get_container_total_volume(self, container: Container) -> float:
        """Calculate total volume of packages in a container."""
        assignments = self.get_container_assignments(container)
        return sum(assignment.package.volume for assignment in assignments)

    def get_container_utilization_score(self, container: Container) -> float:
        """Calculate utilization score for a container (weight + volume efficiency)."""
        weight_utilization = self.get_container_total_weight(container) / container.max_weight
        volume_utilization = self.get_container_total_volume(container) / container.max_volume
        return (weight_utilization + volume_utilization) / 2.0 