<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.acme</groupId>
    <artifactId>task-assigning</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.release>17</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <version.io.quarkus>3.23.2</version.io.quarkus>
        <version.ai.timefold.solver>1.23.0</version.ai.timefold.solver>
        <version.org.apache.commons.text>1.13.1</version.org.apache.commons.text>

        <version.compiler.plugin>3.14.0</version.compiler.plugin>
        <version.resources.plugin>3.3.1</version.resources.plugin>
        <version.surefire.plugin>3.5.3</version.surefire.plugin>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.quarkus</groupId>
                <artifactId>quarkus-bom</artifactId>
                <version>${version.io.quarkus}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>ai.timefold.solver</groupId>
                <artifactId>timefold-solver-bom</artifactId>
                <version>${version.ai.timefold.solver}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>io.quarkus</groupId>
            <artifactId>quarkus-rest</artifactId>
        </dependency>
        <dependency>
            <groupId>io.quarkus</groupId>
            <artifactId>quarkus-rest-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>io.quarkus</groupId>
            <artifactId>quarkus-smallrye-openapi</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.timefold.solver</groupId>
            <artifactId>timefold-solver-quarkus</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.timefold.solver</groupId>
            <artifactId>timefold-solver-quarkus-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>${version.org.apache.commons.text}</version>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>io.quarkus</groupId>
            <artifactId>quarkus-junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.rest-assured</groupId>
            <artifactId>rest-assured</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>ai.timefold.solver</groupId>
            <artifactId>timefold-solver-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>3.27.3</version>
            <scope>test</scope>
        </dependency>

        <!-- UI -->
        <dependency>
            <groupId>io.quarkus</groupId>
            <artifactId>quarkus-webjars-locator</artifactId>
        </dependency>
        <dependency>
            <groupId>ai.timefold.solver</groupId>
            <artifactId>timefold-solver-webui</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>bootstrap</artifactId>
            <version>5.2.3</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>jquery</artifactId>
            <version>3.6.4</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars</groupId>
            <artifactId>font-awesome</artifactId>
            <version>5.15.1</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>js-joda</artifactId>
            <version>1.11.0</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.webjars.npm</groupId>
            <artifactId>js-joda__locale_en-us</artifactId>
            <version>3.1.0</version>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${version.resources.plugin}</version>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${version.compiler.plugin}</version>
            </plugin>
            <plugin>
                <groupId>io.quarkus</groupId>
                <artifactId>quarkus-maven-plugin</artifactId>
                <version>${version.io.quarkus}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${version.surefire.plugin}</version>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>native</id>
            <activation>
                <property>
                    <name>native</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>${version.surefire.plugin}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                                <configuration>
                                    <systemPropertyVariables>
                                        <native.image.path>
                                            ${project.build.directory}/${project.build.finalName}-runner
                                        </native.image.path>
                                    </systemPropertyVariables>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
            <properties>
                <quarkus.native.enabled>true</quarkus.native.enabled>
                <!-- To allow application.properties to avoid using the in-memory database for native builds. -->
                <quarkus.profile>native</quarkus.profile>
            </properties>
        </profile>
        <profile>
            <id>container</id>
            <activation>
                <property>
                    <name>container</name>
                </property>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>io.quarkus</groupId>
                    <artifactId>quarkus-container-image-jib</artifactId>
                </dependency>
            </dependencies>
            <properties>
                <quarkus.container-image.build>true</quarkus.container-image.build>
            </properties>
        </profile>
        <profile>
            <id>enterprise</id>
            <activation>
                <property>
                    <name>enterprise</name>
                </property>
            </activation>
            <repositories>
                <repository>
                    <id>timefold-solver-enterprise</id>
                    <name>Timefold Solver Enterprise Edition</name>
                    <url>https://timefold.jfrog.io/artifactory/releases/</url>
                </repository>
            </repositories>
            <dependencyManagement>
                <dependencies>
                    <dependency>
                        <groupId>ai.timefold.solver.enterprise</groupId>
                        <artifactId>timefold-solver-enterprise-bom</artifactId>
                        <version>${version.ai.timefold.solver}</version>
                        <type>pom</type>
                        <scope>import</scope>
                    </dependency>
                </dependencies>
            </dependencyManagement>
            <dependencies>
                <dependency>
                    <groupId>ai.timefold.solver.enterprise</groupId>
                    <artifactId>timefold-solver-enterprise-quarkus</artifactId>
                </dependency>
            </dependencies>
            <properties>
                <quarkus.profile>enterprise</quarkus.profile>
            </properties>
        </profile>
    </profiles>

</project>
