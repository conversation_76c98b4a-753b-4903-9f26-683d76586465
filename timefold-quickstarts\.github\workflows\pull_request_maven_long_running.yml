name: <PERSON><PERSON>-Running

on:
  # Enables the workflow to run on PRs from forks.
  # CI will only run for trusted users, to prevent stealing of secrets.
  pull_request_target:
    branches: [stable, development, '*.x']
    types:
      - opened
      - reopened
      - synchronize
    paths:
      - 'pom.xml'
      - 'java/**/*'
      - 'kotlin/**/*'
      - '.github/**/*.yml'

jobs:
  # Check if the user is a member of the organization; if so, allow the PR to sail through.
  known_user:
    runs-on: ubuntu-latest
    outputs:
      is_member_of_org: ${{ steps.auth_check.outputs.authorized }}
    steps:
      - id: auth_check
        env:
          GH_TOKEN: ${{ secrets.JRELEASER_GITHUB_TOKEN }}  # Release account is a Solver Gatekeeper.
        shell: bash
        run: |
          # -g to allow actors such as dependabot[bot]
          ORG_MEMBERSHIP=`curl -g -L -H "Accept: application/vnd.github+json" -H "Authorization: Bearer $GH_TOKEN" "https://api.github.com/orgs/TimefoldAI/memberships/${{ github.actor }}" | jq -r '.state == "active"'`
          echo "authorized=$ORG_MEMBERSHIP" >> "$GITHUB_OUTPUT"
      - id: validation
        shell: bash
        run: |
          echo "Authorized user: ${{ steps.auth_check.outputs.authorized }}"
  # If the user is not a member, require a member to approve the PR.
  approval_required:
    needs: known_user
    environment:
      ${{
      github.event_name == 'pull_request_target' &&
      github.event.pull_request.head.repo.full_name != github.repository &&
      (needs.known_user.outputs.is_member_of_org != 'true' || github.actor == 'dependabot[bot]') &&
      'external' || 'internal'
      }}
    runs-on: ubuntu-latest
    steps:
      - run: true
  build-quarkus:
    needs: approval_required
    concurrency:
      group: pull_request_long_running-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.os }}-${{ matrix.module }}-${{ matrix.java-version }}
      cancel-in-progress: true
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest, ubuntu-24.04-arm ]
        module: [
          "kotlin/school-timetabling", "java/bed-allocation",
          "java/conference-scheduling", "java/employee-scheduling", "java/facility-location",
          "java/flight-crew-scheduling", "java/food-packaging", "java/maintenance-scheduling",
          "java/meeting-scheduling", "java/order-picking", "java/project-job-scheduling",
          "java/school-timetabling", "java/sports-league-scheduling", "java/task-assigning",
          "java/tournament-scheduling", "java/vehicle-routing"
        ]
        # Only one supported LTS; already too many jobs here.
        # We choose 21 as Quarkus 3.17.2 has weird issues with Java 17 GraalVM,
        # with Java 21+ GraalVM being recommended even for Java 17 projects.
        # https://github.com/quarkusio/quarkus/issues/44877
        java-version: [ 21 ]
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: ./timefold-quickstarts
          ref: ${{ github.event.pull_request.head.sha }} # The GHA event will pull the main branch by default, and we must specify the PR reference version
      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        id: checkout-solver
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: steps.checkout-solver.outcome != 'success'
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build and test
      - name: "Setup GraalVM native image"
        uses: graalvm/setup-graalvm@01ed653ac833fe80569f1ef9f25585ba2811baab # v1
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'graalvm-community'
          components: 'native-image'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'

      - name: Quickly build timefold-solver
        working-directory: ./timefold-solver
        run: mvn -B -Dquickly clean install
      - name: Test timefold-quickstarts (slow tests)
        working-directory: ./timefold-quickstarts/${{matrix.module}}
        run: mvn -B -Dslowly verify
      - name: Test timefold-quickstarts (native tests)
        working-directory: ./timefold-quickstarts/${{matrix.module}}
        run: mvn -B -Dnative -Pnative verify

  build-enterprise-quarkus:
    needs: approval_required
    concurrency:
      group: pull_request_enterprise_long_running-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.module }}-${{ matrix.java-version }}
      cancel-in-progress: true
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [ "java/bed-allocation", "java/vehicle-routing"]
        # Only one supported LTS; already too many jobs here.
        # We choose 21 as Quarkus 3.17.2 has weird issues with Java 17 GraalVM,
        # with Java 21+ GraalVM being recommended even for Java 17 projects.
        # https://github.com/quarkusio/quarkus/issues/44877
        java-version: [ 21 ]
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: ./timefold-quickstarts
          ref: ${{ github.event.pull_request.head.sha }} # The GHA event will pull the main branch by default, and we must specify the PR reference version
      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        id: checkout-solver
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: steps.checkout-solver.outcome != 'success'
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      - name: Checkout timefold-solver-enterprise (PR) # Checkout the PR branch first, if it exists
        id: checkout-solver-enterprise
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: TimefoldAI/timefold-solver-enterprise
          ref: ${{ github.head_ref }}
          token: ${{ secrets.JRELEASER_GITHUB_TOKEN }} # Safe; only used to clone the repo and not stored in the fork.
          path: ./timefold-solver-enterprise
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver-enterprise (main) # Checkout the main branch if the PR branch does not exist
        if: steps.checkout-solver-enterprise.outcome != 'success'
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver-enterprise
          ref: main
          token: ${{ secrets.JRELEASER_GITHUB_TOKEN }} # Safe; only used to clone the repo and not stored in the fork.
          path: ./timefold-solver-enterprise
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build and test
      - name: "Setup GraalVM native image"
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'graalvm-community'
          components: 'native-image'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'

      - name: Quickly build timefold-solver
        working-directory: ./timefold-solver
        run: mvn -B -Dquickly -DskipTests clean install

      - name: Update timefold-solver-enterprise version (Release PR only)
        if: ${{ startsWith(github.event.pull_request.title, 'build:') && contains(github.event.pull_request.title, 'release version') && github.event.pull_request.user.login == 'timefold-release' }}
        shell: bash
        working-directory: ./timefold-solver-enterprise
        env:
          # PR titles are user-controlled and can contain malicious payloads.
          # Avoid direct shell interpolation.
          PR_TITLE: ${{ github.event.pull_request.title }}
        run: |
          git config user.name "Timefold Release Bot"
          git config user.email "<EMAIL>"
          NEW_VERSION=$(echo "$PR_TITLE" | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
          export NEW_VERSION
          export NEW_VERSION_PYTHON="${NEW_VERSION}"b0
          echo "$NEW_VERSION"
          echo "$NEW_VERSION_PYTHON"
          .github/scripts/change_versions.sh
      - name: Quickly build timefold-solver-enterprise
        working-directory: ./timefold-solver-enterprise
        run: mvn -B -Dquickly -DskipTests clean install

      - name: Test timefold-enterprise-quickstarts (enterprise native tests)
        working-directory: ./timefold-quickstarts/${{matrix.module}}
        run: mvn -B -Dnative -Denterprise -Pnative verify

  build-spring-boot:
    needs: approval_required
    concurrency:
      group: pull_request_long_running-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.module }}-${{ matrix.java-version }}
      cancel-in-progress: true
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: [
          "java/spring-boot-integration"
        ]
        # Only one supported LTS; already too many jobs here.
        # We choose 21 as Quarkus 3.17.2 has weird issues with Java 17 GraalVM,
        # with Java 21+ GraalVM being recommended even for Java 17 projects.
        # https://github.com/quarkusio/quarkus/issues/44877
        java-version: [ 21 ]
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: ./timefold-quickstarts
          ref: ${{ github.event.pull_request.head.sha }} # The GHA event will pull the main branch by default, and we must specify the PR reference version
      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        id: checkout-solver
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: steps.checkout-solver.outcome != 'success'
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build and test
      - name: "Setup GraalVM native image"
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'graalvm-community'
          components: 'native-image'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          cache: 'maven'

      - name: Quickly build timefold-solver
        working-directory: ./timefold-solver
        run: mvn -B -Dquickly clean install
      - name: Test timefold-quickstarts (slow tests)
        working-directory: ./timefold-quickstarts/${{matrix.module}}
        run: mvn -B -Dslowly verify
      - name: Test timefold-quickstarts (native tests)
        working-directory: ./timefold-quickstarts/${{matrix.module}}
        run: mvn -B -PnativeTest verify
