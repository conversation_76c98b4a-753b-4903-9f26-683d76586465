plugins {
    id "org.springframework.boot" version "3.5.0"
    id "io.spring.dependency-management" version "1.1.7"
    id 'org.graalvm.buildtools.native' version '0.10.6'
    id "java"
}

def timefoldVersion = "1.23.0"
def profile = System.properties['profile'] ?: ''
def enterprise = System.properties['enterprise'] ?: ''

group = "org.acme"
archivesBaseName = "spring-boot-school-timetabling"
version = "1.0-SNAPSHOT"

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

bootRun {
    if (enterprise == 'true') {
        jvmArgs(["-Dspring.profiles.active=enterprise"])
    }
}

repositories {
    mavenCentral()
    mavenLocal()
}

dependencies {
    implementation platform("ai.timefold.solver:timefold-solver-bom:${timefoldVersion}")
    if (enterprise == 'true') {
        implementation platform("ai.timefold.solver.enterprise:timefold-solver-enterprise-bom:${timefoldVersion}")
        implementation "ai.timefold.solver.enterprise:timefold-solver-enterprise-quarkus"
    }

    // Spring-boot
    implementation "org.springframework.boot:spring-boot-starter-web"
    implementation "ai.timefold.solver:timefold-solver-spring-boot-starter"

    // Swagger
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.8"

    // Testing
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("ai.timefold.solver:timefold-solver-test")
    testImplementation("org.springframework:spring-webflux")
    testImplementation "org.awaitility:awaitility"
    testImplementation "org.assertj:assertj-core:3.27.3"

    // UI
    // No webjar locator; incompatible in native mode;
    // see https://github.com/spring-projects/spring-framework/issues/27619
    // and https://github.com/webjars/webjars-locator-core/issues/96
    // runtimeOnly "org.webjars:webjars-locator:0.50"
    runtimeOnly "ai.timefold.solver:timefold-solver-webui"
    runtimeOnly "org.webjars:bootstrap:5.2.3"
    runtimeOnly "org.webjars:jquery:3.6.4"
    runtimeOnly "org.webjars:font-awesome:5.15.1"
    runtimeOnly "org.webjars.npm:js-joda:1.11.0"
}

test {
    // Gradle needs native tests in src/native-test/java, but maven needs them in src/test/java instead.
    // Maven first, so we skip them in Gradle unfortunately.
    exclude "**/**IT.class"

    // Log the test execution results.
    testLogging {
        events "passed", "skipped", "failed"
    }

    if (profile == 'slowly') {
        useJUnitPlatform()
    } else {
        useJUnitPlatform {
            excludeTags "slowly"
        }
    }
}

// optimizedLaunch disables the C2 compiler, which has a massive performance impact
tasks.named("bootRun") {
    optimizedLaunch = false
}
