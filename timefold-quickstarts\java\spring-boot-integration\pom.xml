<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <!-- Use spring boot parent to use its native profile -->
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.5.0</version>
  </parent>

  <groupId>org.acme</groupId>
  <artifactId>spring-boot-school-timetabling</artifactId>
  <version>1.0-SNAPSHOT</version>

  <properties>
    <maven.compiler.release>17</maven.compiler.release>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <version.ai.timefold.solver>1.23.0</version.ai.timefold.solver>
    <version.org.springframework.boot>${project.parent.version}</version.org.springframework.boot>

    <version.compiler.plugin>3.14.0</version.compiler.plugin>
    <version.surefire.plugin>3.5.3</version.surefire.plugin>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${version.org.springframework.boot}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>ai.timefold.solver</groupId>
        <artifactId>timefold-solver-bom</artifactId>
        <version>${version.ai.timefold.solver}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Spring boot -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>ai.timefold.solver</groupId>
      <artifactId>timefold-solver-spring-boot-starter</artifactId>
    </dependency>

    <!-- Swagger -->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
      <version>2.8.8</version>
    </dependency>

    <!-- Testing -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>ai.timefold.solver</groupId>
      <artifactId>timefold-solver-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webflux</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- UI -->
    <!-- No webjar locator; incompatible in native mode;
         see https://github.com/spring-projects/spring-framework/issues/27619
         and https://github.com/webjars/webjars-locator-core/issues/96
     -->
    <dependency>
      <groupId>ai.timefold.solver</groupId>
      <artifactId>timefold-solver-webui</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.webjars</groupId>
      <artifactId>bootstrap</artifactId>
      <version>5.2.3</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.webjars</groupId>
      <artifactId>jquery</artifactId>
      <version>3.6.4</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.webjars</groupId>
      <artifactId>font-awesome</artifactId>
      <version>5.15.1</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.webjars.npm</groupId>
      <artifactId>js-joda</artifactId>
      <version>1.11.0</version>
      <scope>runtime</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>${version.compiler.plugin}</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${version.surefire.plugin}</version>
      </plugin>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${version.org.springframework.boot}</version>
        <executions>
          <!-- Repackage the archive produced by maven-jar-plugin into an executable JAR file. -->
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <!-- optimizedLaunch disables the C2 compiler, which has a massive performance impact -->
          <optimizedLaunch>false</optimizedLaunch>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.graalvm.buildtools</groupId>
        <artifactId>native-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>enterprise</id>
      <activation>
        <property>
          <name>enterprise</name>
        </property>
      </activation>
      <repositories>
        <repository>
          <id>timefold-solver-enterprise</id>
          <name>Timefold Solver Enterprise Edition</name>
          <url>https://timefold.jfrog.io/artifactory/releases/</url>
        </repository>
      </repositories>
      <dependencyManagement>
        <dependencies>
          <dependency>
            <groupId>ai.timefold.solver.enterprise</groupId>
            <artifactId>timefold-solver-enterprise-bom</artifactId>
            <version>${version.ai.timefold.solver}</version>
            <type>pom</type>
            <scope>import</scope>
          </dependency>
        </dependencies>
      </dependencyManagement>
      <dependencies>
        <dependency>
          <groupId>ai.timefold.solver.enterprise</groupId>
          <artifactId>timefold-solver-enterprise-quarkus</artifactId>
        </dependency>
      </dependencies>
      <properties>
        <spring-boot.run.profiles>enterprise</spring-boot.run.profiles>
      </properties>
    </profile>
  </profiles>


</project>
