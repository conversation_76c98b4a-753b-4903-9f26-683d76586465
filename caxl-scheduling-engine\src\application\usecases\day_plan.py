"""
DayPlan Use Case - Stage 2 of healthcare scheduling optimization.

This use case optimizes timing and routing within a day for assigned appointments.
It is the second stage of the 2-stage optimization process:
1. Assignment Solver: Assigns providers and dates to appointments (AssignAppointment use case)
2. Day Plan Solver: Optimizes timing and routing within a day (this use case)
"""

import logging
import os
import sys
import time
from datetime import date, datetime, timedelta
from typing import List, Dict, Any, Optional
from uuid import uuid4

from timefold.solver import SolverFactory, SolutionManager
from timefold.solver.config import SolverConfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver.domain import (
    PlanningId, PlanningVariable, PlanningEntityCollectionProperty, 
    ProblemFactCollectionProperty, ValueRangeProvider, PlanningScore
)
from timefold.solver.score import HardSoftScore

from loguru import logger

# Import domain entities
from src.domain.entities.appointment import (
    AppointmentData, AppointmentAssignment, AppointmentSchedule
)
from src.domain.entities.provider import Provider
from src.domain.entities.consumer import Consumer
from src.domain.models.config import SchedulerConfig, ServiceConfig, BatchAssignmentResult, AssignmentResult

# Import infrastructure components
from src.infrastructure.config.config_manager import ConfigManager
from src.infrastructure.data.data_loader import create_demo_data
from src.infrastructure.bridges.scheduler_bridge import define_constraints


class DayPlanUseCase:
    """Use case for optimizing timing and routing within a day."""
    
    def __init__(self, config_manager: Optional[ConfigManager] = None, daemon_mode: bool = False):
        """Initialize the day plan use case."""
        self.config_manager = config_manager or ConfigManager()
        self.scheduler_config = self.config_manager.get_scheduler_config()
        self.service_configs = self.config_manager.get_all_service_configs()
        self.daemon_mode = daemon_mode
        
        # Initialize solver with proper configuration for day planning
        solver_config = SolverConfig(
            solution_class=AppointmentSchedule,
            entity_class_list=[AppointmentAssignment],
            score_director_factory_config=ScoreDirectorFactoryConfig(
                constraint_provider_function=define_constraints
            ),
            termination_config=TerminationConfig(
                spent_limit=Duration(seconds=60),  # 1 minute for day planning
                unimproved_spent_limit=Duration(seconds=15)  # Stop if no improvement for 15 seconds
            )
        )
        
        self.solver_factory = SolverFactory.create(solver_config)
        self.solution_manager = SolutionManager.create(self.solver_factory)
        
        logger.info("DayPlan use case initialized with solver config")
    
    def execute(self, target_date: Optional[date] = None) -> Dict[str, Any]:
        """Execute the day planning use case."""
        start_time = time.time()
        
        if target_date is None:
            target_date = date.today()
        
        logger.info("[START] DAY PLAN USE CASE STARTED")
        logger.info(f"[INFO] Target Date: {target_date}")
        
        try:
            # STAGE 1: Load demo data
            logger.info("[INFO] STAGE 1: Loading Data")
            demo_data = create_demo_data()
            providers = demo_data["providers"]
            consumers = demo_data["consumers"]
            appointments = demo_data["appointments"]
            
            logger.info(f"[INFO] Initial data loaded: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")
            
            # STAGE 2: Filter appointments for the target date
            logger.info(f"[INFO] STAGE 2: Filtering Appointments for {target_date}")
            day_appointments = self._filter_appointments_for_date(appointments, target_date)
            logger.info(f"[INFO] Found {len(day_appointments)} appointments for {target_date}")
            
            if not day_appointments:
                logger.warning("[WARN] No appointments found for the target date")
                result = {
                    "success": True,
                    "message": "No appointments found for the target date",
                    "assignments": [],
                    "processing_time": time.time() - start_time
                }
                self._handle_completion(result)
                return result
            
            # STAGE 3: Create planning entities for day planning
            logger.info("[INFO] STAGE 3: Creating Planning Entities")
            domain_assignments = self._create_domain_assignments(day_appointments)
            planning_assignments = self._create_planning_assignments(day_appointments)
            logger.info(f"[INFO] Created {len(planning_assignments)} planning entities")
            
            # STAGE 4: Create time slots for the day
            logger.info("[INFO] STAGE 4: Creating Time Slots")
            time_slots = self._create_time_slots(target_date)
            logger.info(f"[INFO] Created {len(time_slots)} time slots")
            
            # STAGE 5: Create solution
            logger.info("[INFO] STAGE 5: Creating Optimization Solution")
            domain_solution = AppointmentSchedule(
                id=str(uuid4()),
                providers=providers,
                available_dates=[target_date],
                appointment_assignments=domain_assignments
            )
            solution = self._create_planning_schedule(domain_solution)
            logger.info(f"[INFO] Solution created with {len(planning_assignments)} assignments")
            
            # STAGE 6: Solve the day planning problem
            logger.info("[INFO] STAGE 6: Starting Day Planning Optimization")
            solved_solution = self._solve_day_planning_problem(solution)
            logger.info("[INFO] Day planning optimization completed")
            
            # STAGE 7: Process results
            logger.info("[INFO] STAGE 7: Processing Results")
            results = self._process_day_planning_results(solved_solution, consumers, start_time)
            
            logger.info("[COMPLETE] DAY PLAN USE CASE COMPLETED")
            logger.info(f"[INFO] Total processing time: {results['processing_time']:.2f} seconds")
            
            self._handle_completion(results)
            return results
            
        except Exception as e:
            logger.error(f"[ERROR] Error in DayPlan use case: {e}", exc_info=True)
            result = {
                "success": False,
                "error": str(e),
                "processing_time": time.time() - start_time
            }
            self._handle_completion(result)
            return result
    
    def _filter_appointments_for_date(self, appointments: List[AppointmentData], target_date: date) -> List[AppointmentData]:
        """Filter appointments for a specific date."""
        filtered_appointments = []
        for appointment in appointments:
            if appointment.appointment_date == target_date:
                filtered_appointments.append(appointment)
        return filtered_appointments
    
    def _create_domain_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create domain assignment objects."""
        assignments = []
        for appointment in appointments:
            assignment = AppointmentAssignment(
                id=f"dayplan_{appointment.id}",
                appointment_data=appointment,
                provider=None,  # Will be assigned by solver
                assigned_date=appointment.appointment_date  # Already assigned date
            )
            assignments.append(assignment)
        return assignments
    
    def _create_planning_assignments(self, appointments: List[AppointmentData]) -> List[AppointmentAssignment]:
        """Create planning assignment entities for the solver."""
        return self._create_domain_assignments(appointments)
    
    def _create_time_slots(self, target_date: date) -> List[str]:
        """Create time slots for the day."""
        time_slots = []
        start_hour = 8  # 8 AM
        end_hour = 18   # 6 PM
        
        for hour in range(start_hour, end_hour):
            for minute in [0, 30]:  # 30-minute slots
                time_slot = f"{target_date.strftime('%Y-%m-%d')}T{hour:02d}:{minute:02d}:00"
                time_slots.append(time_slot)
        
        return time_slots
    
    def _create_planning_schedule(self, domain_solution: AppointmentSchedule) -> AppointmentSchedule:
        """Convert domain solution to planning solution for solver."""
        return domain_solution
    
    def _solve_day_planning_problem(self, solution: AppointmentSchedule) -> AppointmentSchedule:
        """Solve the day planning problem using Timefold."""
        logger.info(f"Solving day planning problem with {len(solution.appointment_assignments)} appointments")
        
        # Create solver
        solver = self.solver_factory.build_solver()
        
        # Solve the problem
        solved_solution = solver.solve(solution)
        
        # Get solution info
        solution_info = self.solution_manager.explain(solved_solution)
        logger.info(f"Solution score: {solved_solution.score}")
        logger.info(f"Solution info: {solution_info}")
        
        return solved_solution
    
    def _process_day_planning_results(self, solution: AppointmentSchedule, consumers: List[Consumer], start_time: float) -> Dict[str, Any]:
        """Process the day planning results and create response."""
        processing_time = time.time() - start_time
        
        # Create results list
        results = []
        assigned_count = 0
        unassigned_count = 0
        
        for assignment in solution.appointment_assignments:
            if assignment.provider:
                assigned_count += 1
                result = AssignmentResult(
                    appointment_id=str(assignment.appointment_data.id),
                    patient_id=str(assignment.appointment_data.consumer_id),
                    provider_id=str(assignment.provider.id),
                    time_slot_id=f"{assignment.assigned_date}",
                    score=0.0,
                    constraints_satisfied=[],
                    constraints_violated=[]
                )
                results.append(result)
            else:
                unassigned_count += 1
        
        # Create batch result
        batch_result = BatchAssignmentResult(
            batch_id=f"dayplan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            total_appointments=len(solution.appointment_assignments),
            assigned_appointments=assigned_count,
            unassigned_appointments=unassigned_count,
            average_score=0.0,
            processing_time_seconds=processing_time,
            results=results
        )
        
        return {
            "success": True,
            "batch_result": batch_result,
            "assignments": [str(assignment) for assignment in solution.appointment_assignments],
            "processing_time": processing_time
        }
    
    def _handle_completion(self, results: Dict[str, Any]):
        """Handle completion of the use case."""
        if self.daemon_mode:
            # In daemon mode, we might want to publish results or store them
            logger.info("Daemon mode: Results ready for API consumption")
        else:
            # In standalone mode, just log the results
            logger.info(f"Standalone mode: Results completed - {results.get('success', False)}")


def main(daemon_mode: bool = False):
    """Main entry point for the day plan use case."""
    use_case = DayPlanUseCase(daemon_mode=daemon_mode)
    return use_case.execute()


if __name__ == "__main__":
    main() 