# Scenario-Based Testing Guide

## Overview

The CAXL Scheduling Engine includes a comprehensive scenario-based testing framework that allows you to test various edge cases, constraints, and optimization scenarios using predefined YAML datasets. This framework ensures that all scheduling constraints and edge cases are properly tested and validated.

## Why Scenario-Based Testing?

1. **Comprehensive Coverage**: Test various real-world scenarios without manual data setup
2. **Edge Case Validation**: Ensure the system handles challenging cases gracefully
3. **Constraint Testing**: Verify that all business rules and constraints are enforced
4. **Regression Prevention**: Catch issues before they reach production
5. **Demo Capability**: Show different features to stakeholders with realistic data

## Available Scenarios

### 1. Basic Demo (`basic_demo/`)
- **Purpose**: Simple demonstration of core functionality
- **Complexity**: Low
- **Best for**: Initial demos, basic feature overview
- **Features**: Basic assignment, skill matching, geographic areas

### 2. Edge Cases (`edge_cases/`)
- **Purpose**: Test various edge cases and constraint violations
- **Complexity**: High
- **Best for**: Stress testing, validation of error handling
- **Features**: Capacity overload, skill mismatches, geographic conflicts, urgent appointments

## Scenario Structure

Each scenario consists of three YAML files:

```
data/scenarios/{scenario_name}/
├── README.md           # Scenario documentation
├── appointments.yml    # Appointment data
├── providers.yml       # Provider data
└── consumers.yml       # Consumer/patient data
```

### YAML File Format

#### appointments.yml
```yaml
appointments:
  - id: "apt-001"
    consumer_id: "patient-001"
    appointment_date: "2025-01-15"
    required_skills: ["medication_management"]
    duration_min: 60
    urgent: false
    location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      address: "123 Park Avenue, New York, NY 10016"
```

#### providers.yml
```yaml
providers:
  - id: "provider-001"
    name: "Sarah Johnson, RN"
    role: "RN"
    skills: ["medication_management", "wound_care"]
    home_location:
      latitude: 40.7589
      longitude: -73.9851
      city: "New York"
      state: "NY"
      address: "123 5th Avenue, New York, NY 10003"
    availability:
      working_days: ["monday", "tuesday", "wednesday", "thursday", "friday"]
      working_hours: ["08:00", "17:00"]
    capacity:
      max_tasks_count_in_day: 6
      max_hours_per_day: 8
```

#### consumers.yml
```yaml
consumers:
  - id: "patient-001"
    name: "John Smith"
    location:
      latitude: 40.7580
      longitude: -73.9855
      city: "New York"
      state: "NY"
      address: "123 Park Avenue, New York, NY 10016"
    care_episode_id: "episode-001"
```

## Using Scenarios

### Via API

#### List Available Scenarios
```bash
curl http://localhost:8000/scenarios
```

#### Get Scenario Details
```bash
curl http://localhost:8000/scenarios/basic_demo
```

#### Load Data with Scenario
```bash
# Get appointments for a specific scenario
curl "http://localhost:8000/appointments?scenario=basic_demo"

# Get providers for a specific scenario
curl "http://localhost:8000/providers?scenario=edge_cases"

# Get consumers for a specific scenario
curl "http://localhost:8000/consumers?scenario=basic_demo"
```

#### Run Jobs with Scenarios
```bash
# Run assignment job with scenario
curl -X POST http://localhost:8000/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{
    "target_date": "2025-01-15",
    "scenario": "basic_demo"
  }'

# Run day plan job with scenario
curl -X POST http://localhost:8000/day-plan \
  -H "Content-Type: application/json" \
  -d '{
    "target_date": "2025-01-15",
    "scenario": "edge_cases"
  }'
```

### Via Python Script

#### Run Comprehensive Tests
```bash
python test_scenarios.py
```

#### Load Scenario in Code
```python
from src.infrastructure.data.data_loader import create_demo_data

# Load basic demo scenario
demo_data = create_demo_data("basic_demo")
providers = demo_data["providers"]
consumers = demo_data["consumers"]
appointments = demo_data["appointments"]

# Load edge cases scenario
edge_data = create_demo_data("edge_cases")
```

## Edge Cases Testing

The `edge_cases` scenario includes various challenging scenarios:

### 1. Capacity Overload
- **Description**: More appointments than providers can handle
- **Test**: 15 appointments for 3 providers (max 6 each)
- **Expected**: System should handle gracefully when not all appointments can be scheduled

### 2. Skill Mismatches
- **Description**: Appointments requiring skills no provider has
- **Test**: Appointment requiring "cardiac_specialist" skill
- **Expected**: Skill mismatches should be clearly identified

### 3. Geographic Conflicts
- **Description**: Appointments outside provider service areas
- **Test**: Appointment in Albany (50 miles from Manhattan providers)
- **Expected**: Geographic violations should be logged

### 4. Urgent Appointments
- **Description**: Mix of urgent and regular appointments
- **Test**: 2 urgent appointments competing with 13 regular ones
- **Expected**: Urgent appointments should get priority

### 5. Provider Blacklists
- **Description**: Providers who can't serve specific patients
- **Test**: Provider with blacklisted consumer
- **Expected**: Blacklist constraints should be enforced

### 6. Time Conflicts
- **Description**: Overlapping appointments for same provider
- **Test**: Two 2-hour appointments at same time
- **Expected**: Time conflicts should be detected

### 7. Dependencies
- **Description**: Appointments that must be scheduled together
- **Test**: 3 appointments that must be sequential
- **Expected**: Dependency constraints should be maintained

## Creating New Scenarios

### 1. Create Scenario Directory
```bash
mkdir data/scenarios/my_new_scenario
```

### 2. Create YAML Files
Create the three required YAML files with your test data.

### 3. Add Documentation
Create a `README.md` file describing:
- Purpose of the scenario
- Complexity level
- Features being tested
- Expected outcomes
- Configuration requirements

### 4. Test Your Scenario
```bash
# Test loading
python test_scenarios.py

# Test via API
curl "http://localhost:8000/appointments?scenario=my_new_scenario"
```

## Validation and Testing

### Automated Validation
The testing framework automatically validates:
- Data integrity (required fields, valid IDs)
- Skill matching between appointments and providers
- Geographic proximity
- Capacity constraints
- Provider availability

### Manual Testing
```python
# Test specific scenarios
from test_scenarios import test_edge_cases, test_basic_demo

test_edge_cases()    # Test edge cases
test_basic_demo()    # Test basic functionality
```

## Best Practices

### 1. Scenario Design
- Keep scenarios focused on specific features or edge cases
- Use realistic data that represents actual use cases
- Include both positive and negative test cases
- Document the expected behavior clearly

### 2. Data Quality
- Ensure all required fields are present
- Use consistent naming conventions
- Validate geographic coordinates
- Test with various skill combinations

### 3. Testing Strategy
- Run scenarios before and after code changes
- Use edge cases to validate error handling
- Test performance with larger datasets
- Validate constraint enforcement

### 4. Documentation
- Keep README files up to date
- Document any special configuration requirements
- Explain the business logic behind test cases
- Include examples of expected outputs

## Troubleshooting

### Common Issues

#### Scenario Not Found
```
Error: Scenario directory not found
```
**Solution**: Check that the scenario directory exists and contains the required YAML files.

#### Invalid YAML
```
Error: Failed to parse YAML file
```
**Solution**: Validate YAML syntax using a YAML validator.

#### Missing Required Fields
```
Warning: Provider missing ID
```
**Solution**: Ensure all required fields are present in your YAML files.

#### Import Errors
```
Error: Module not found
```
**Solution**: Ensure you're running from the correct directory and Python path is set correctly.

### Debug Mode
Enable debug logging to see detailed information:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Integration with CI/CD

### Automated Testing
Include scenario testing in your CI/CD pipeline:

```yaml
# .github/workflows/test-scenarios.yml
name: Test Scenarios
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Run scenario tests
        run: python test_scenarios.py
```

### Performance Testing
Test scenarios with larger datasets to validate performance:
```python
# Create performance test scenario
def create_performance_scenario():
    # Generate 1000 appointments, 100 providers
    # Test system performance under load
    pass
```

## Conclusion

The scenario-based testing framework provides a robust way to validate the scheduling engine's behavior across various real-world scenarios. By using predefined datasets, you can ensure comprehensive testing coverage and catch issues early in the development process.

For more information, see:
- [API Documentation](README.md#api-endpoints)
- [Configuration Guide](README.md#configuration)
- [Development Guide](README.md#development) 