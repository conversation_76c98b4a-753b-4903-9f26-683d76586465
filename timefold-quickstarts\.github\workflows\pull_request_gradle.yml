name: Gradle

on:
  push:
    branches: [stable, development, '*.x']
    paths:
      - 'settings.gradle'
      - 'java/**/*'
      - 'kotlin/**/*'
      - '.github/**/*.yml'
  pull_request:
    branches: [stable, development, '*.x']
    paths:
      - 'settings.gradle'
      - 'java/**/*'
      - 'kotlin/**/*'
      - '.github/**/*.yml'

jobs:
  build:
    concurrency:
      group: pull_request_gradle-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.module }}
      cancel-in-progress: true
    runs-on: ubuntu-latest
    strategy:
      matrix:
        module: ['java/hello-world', 'java/school-timetabling', 'java/spring-boot-integration']
        java-version: [ 17 ] # Only the first supported LTS; already too many jobs here.
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: './timefold-quickstarts'

      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        if: github.head_ref # Only true if this is a PR.
        id: checkout-solver-pr
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: ${{ steps.checkout-solver-pr.outcome != 'success' }}
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build the solver
      - name: "Setup Java and Gradle"
        uses: actions/setup-java@v3
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'temurin'
          cache: 'gradle'
      - name: Quickly build timefold-solver
        working-directory: ./timefold-solver
        run: mvn -B -Dquickly clean install

      - name: Build and test timefold-quickstarts
        working-directory: ./timefold-quickstarts/${{ matrix.module }}
        run: gradle build
