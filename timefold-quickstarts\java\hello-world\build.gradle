plugins {
    id "java"
    id "application"
}

def timefoldVersion = "1.23.0"
def logbackVersion = "1.5.18"
def junitVersion = "5.13.1"
def assertjVersion = "3.27.3"
def profile = System.properties['profile'] ?: ''

group = "org.acme"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
    mavenLocal()
}

dependencies {
    implementation platform("ai.timefold.solver:timefold-solver-bom:${timefoldVersion}")
    implementation "ai.timefold.solver:timefold-solver-core"
    runtimeOnly "ch.qos.logback:logback-classic:${logbackVersion}"

    // Testing
    testImplementation platform("org.junit:junit-bom:${junitVersion}")
    testImplementation "ai.timefold.solver:timefold-solver-test"
    testImplementation "org.junit.jupiter:junit-jupiter"
    testImplementation "org.assertj:assertj-core:${assertjVersion}"
    testRuntimeOnly "org.junit.platform:junit-platform-launcher"

}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

compileJava {
    options.encoding = "UTF-8"
    options.compilerArgs << "-parameters"
}

compileTestJava {
    options.encoding = "UTF-8"
}

application {
    mainClass = "org.acme.schooltimetabling.TimetableApp"
}

test {
    // Log the test execution results.
    testLogging {
        events "passed", "skipped", "failed"
    }

    if (profile == 'slowly') {
        useJUnitPlatform()
    } else {
        useJUnitPlatform {
            excludeTags "slowly"
        }
    }
}
