== Contributing to Timefold Quickstarts

This is an open source project, and you are more than welcome to contribute!

* Found an issue? https://github.com/TimefoldAI/timefold-quickstarts/issues[Submit an issue.]
* Want to fix an issue or contribute an improvement? https://github.com/TimefoldAI/timefold-solver/discussions[Talk to us about your ideas] or just start coding:

. https://github.com/TimefoldAI/timefold-quickstarts/fork[Fork it.]
. Create a feature branch: `git checkout -b feature`
. Commit your changes with a comment: `git commit -m "feat: add shiny new feature"`
(See xref:commit-messages[Commit messages] for details.)
. Push to the branch to GitHub: `git push origin feature`
. https://github.com/TimefoldAI/timefold-quickstarts/compare/development...development[Create a new Pull Request.]

The CI checks against your PR to ensure that it doesn't introduce errors.
If the CI identifies a potential problem, our friendly PR maintainers will help you resolve it.


=== Developing Python quickstarts

The Python quickstarts on the `development` branch uses the
development `999-dev0` build of `timefold-solver`.
This needs to be built locally:

. <PERSON>lone https://github.com/TimefoldAI/timefold-solver-python[timefold-solver-python].

. Install `build`
+
[source,base]
----
$ pip install build
----

. Build the main branch of `timefold-solver-python`
+
[source,base]
----
$ python -m build
----

. Reference the `timefold-solver-python` build directory in your `~/.config/pip/pip.conf`:
+
[source,ini]
----
[global]
find-links =
    /path/to/timefold-solver-python/dist
----
