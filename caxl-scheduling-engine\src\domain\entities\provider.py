"""
Provider domain entities for healthcare scheduling optimization.
"""

from dataclasses import dataclass, field
from datetime import date, datetime, time, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel


class DateSpecificProviderAvailability(BaseModel):
    """Date-specific provider availability override (e.g., half-day leave, late start)."""

    date: date                                      # Specific date
    available_start: Optional[time] = None          # Available from this time (None = not available)
    available_end: Optional[time] = None            # Available until this time (None = not available)
    reason: Optional[str] = None                    # "half_day_leave", "medical_appointment", "training"

    @property
    def is_full_day_off(self) -> bool:
        """Check if provider is completely unavailable on this date."""
        return self.available_start is None and self.available_end is None

    @property
    def is_partial_day(self) -> bool:
        """Check if provider has partial availability on this date."""
        return (self.available_start is not None and self.available_end is not None and
                self.available_start != time(0, 0) and self.available_end != time(23, 59))


class ShiftPattern(BaseModel):
    """Healthcare shift pattern definition."""

    shift_name: str                                 # "day_shift", "night_shift", "weekend_shift"
    shift_start: time                               # Shift start time
    shift_end: time                                 # Shift end time
    crosses_midnight: bool = False                  # True if shift spans midnight (e.g., 11 PM - 7 AM)
    shift_days: List[str] = []                  # Days this shift applies to

    @property
    def duration_hours(self) -> float:
        """Calculate shift duration in hours."""
        if self.crosses_midnight:
            # Handle shifts that cross midnight
            start_minutes = self.shift_start.hour * 60 + self.shift_start.minute
            end_minutes = self.shift_end.hour * 60 + self.shift_end.minute
            if end_minutes < start_minutes:
                end_minutes += 24 * 60  # Add 24 hours
            return (end_minutes - start_minutes) / 60.0
        else:
            # Normal shift within same day
            start_minutes = self.shift_start.hour * 60 + self.shift_start.minute
            end_minutes = self.shift_end.hour * 60 + self.shift_end.minute
            return (end_minutes - start_minutes) / 60.0


class ProviderAvailability(BaseModel):
    """Provider availability schedule - healthcare shift-based model."""

    # Primary shift assignment
    primary_shift: Optional[ShiftPattern] = None    # Provider's main shift pattern

    # Additional shifts (for providers who work multiple shift types)
    additional_shifts: List[ShiftPattern] = []      # Extra shifts (overtime, coverage)

    # Working days - days provider is scheduled to work
    working_days: List[str] = ["monday", "tuesday", "wednesday", "thursday", "friday"]

    # Standard break periods - applies to all working days
    break_periods: List[tuple[time, time]] = [
        (time(12, 0), time(13, 0)),  # Standard lunch break 12-1 PM
    ]

    # Split shifts for providers who work multiple shifts per day
    split_shifts: List[tuple[time, time]] = []  # [(start1, end1), (start2, end2), ...]

    # Working hours for providers without defined shifts (simple hour ranges)
    working_hours: Optional[tuple[time, time]] = None  # (start_time, end_time) for simple hour ranges

    # Holiday dates when provider is not available
    holidays: List[date] = []  # List of holiday dates

    # Date-specific availability overrides (half-day leave, appointments, etc.)
    date_specific_availability: List[DateSpecificProviderAvailability] = []

    # Time off periods - full days when provider is unavailable (vacation, sick days)
    time_off_periods: List[tuple[date, date]] = []  # [(start_date, end_date), ...]

    # Organizational constraints
    max_hours_per_day: int = 8                      # Standard organizational limit
    max_hours_per_week: int = 40                    # Standard organizational limit

    # Additional scheduling constraints for timeslot validation
    unavailable_time_slots: List[tuple[time, time]] = []  # Specific time slots when provider is unavailable
    max_appointment_duration_min: Optional[int] = None    # Maximum appointment duration in minutes

    # Operational constraints
    min_gap_between_assignments: int = 30           # Travel/prep time in minutes

    # Overtime policies
    overtime_allowed: bool = False
    max_overtime_hours_per_week: Optional[int] = None

    # On-call availability
    on_call_available: bool = False
    on_call_days: List[str] = []

    # Extension point
    properties: Dict[str, Any] = {}

    def get_shift_hours(self, specific_date: date) -> Optional[tuple[time, time]]:
        """Get shift hours for a specific date, considering overrides."""
        # Check for date-specific availability override
        for override in self.date_specific_availability:
            if override.date == specific_date:
                if override.is_full_day_off:
                    return None
                elif override.is_partial_day:
                    # Ensure both start and end times are not None before returning
                    if override.available_start is not None and override.available_end is not None:
                        return (override.available_start, override.available_end)
                    else:
                        # If either time is None, treat as not available
                        return None
                else:
                    # Full day available
                    return self._get_default_shift_hours(specific_date.weekday())

        # Check if date is in time off period
        for start_date, end_date in self.time_off_periods:
            if start_date <= specific_date <= end_date:
                return None

        # Use default shift hours for the weekday
        weekday = specific_date.weekday()
        return self._get_default_shift_hours(weekday)

    def _get_default_shift_hours(self, weekday: int) -> Optional[tuple[time, time]]:
        """Get default shift hours for a weekday."""
        weekday_names = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
        weekday_name = weekday_names[weekday]
        
        if weekday_name not in self.working_days:
            return None
        
        if self.primary_shift and weekday_name in self.primary_shift.shift_days:
            return (self.primary_shift.shift_start, self.primary_shift.shift_end)
        
        if self.working_hours:
            return self.working_hours
        
        return None

    def is_available_at_time(self, specific_datetime: datetime) -> bool:
        """Check if provider is available at a specific datetime."""
        specific_date = specific_datetime.date()
        specific_time = specific_datetime.time()
        
        # Get shift hours for the date
        shift_hours = self.get_shift_hours(specific_date)
        if not shift_hours:
            return False
        
        start_time, end_time = shift_hours
        
        # Check if time is within shift hours
        if self._shift_crosses_midnight(start_time, end_time):
            # Handle shifts that cross midnight
            if specific_time >= start_time or specific_time <= end_time:
                return True
        else:
            # Normal shift within same day
            if start_time <= specific_time <= end_time:
                return True
        
        return False

    def _shift_crosses_midnight(self, start_time: time, end_time: time) -> bool:
        """Check if shift crosses midnight."""
        return end_time < start_time

    def is_time_within_split_shifts(self, check_time: time) -> bool:
        """Check if time falls within any split shift period."""
        for start_time, end_time in self.split_shifts:
            if self._shift_crosses_midnight(start_time, end_time):
                if check_time >= start_time or check_time <= end_time:
                    return True
            else:
                if start_time <= check_time <= end_time:
                    return True
        return False

    def get_all_available_shifts_for_day(self, weekday: int) -> List[tuple[time, time]]:
        """Get all available shifts for a specific weekday."""
        shifts = []
        
        if self.primary_shift:
            weekday_names = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            weekday_name = weekday_names[weekday]
            if weekday_name in self.primary_shift.shift_days:
                shifts.append((self.primary_shift.shift_start, self.primary_shift.shift_end))
        
        for additional_shift in self.additional_shifts:
            weekday_names = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]
            weekday_name = weekday_names[weekday]
            if weekday_name in additional_shift.shift_days:
                shifts.append((additional_shift.shift_start, additional_shift.shift_end))
        
        return shifts


class ProviderCapacity(BaseModel):
    """Daily workload limits and capacity constraints for healthcare providers."""

    # Daily workload limits (required for optimization)
    max_allocated_task_points_in_day: int = 27             # Max complexity points per day
    max_tasks_count_in_day: int = 6                        # Max number of tasks per day
    max_hours_per_day: int = 8                             # Maximum working hours per day

    # Additional capacity constraints
    max_consecutive_tasks: int = 4                         # Max tasks without break
    min_break_between_tasks: int = 15                      # Minimum minutes between tasks

    # Extension point for capacity-specific attributes
    properties: Dict[str, Any] = {}


class ProviderPreferences(BaseModel):
    """Provider preferences and restrictions for assignment optimization."""

    # Consumer preferences
    blacklisted_consumers: List[str] = []                  # Specific patients provider won't serve
    preferred_consumers: List[str] = []                    # Specific patients provider prefers

    # Geographic preferences
    blackout_areas: List[str] = []                         # Geographic areas to avoid

    # Task preferences
    preferred_task_types: List[str] = []                   # Task types provider prefers
    blacklisted_task_types: List[str] = []                 # Task types provider won't do

    # Extension point for preference-specific attributes
    properties: Dict[str, Any] = {}


@dataclass
class Provider:
    """Healthcare provider or service provider."""
    id: UUID
    name: str

    # ALL properties promoted to first-class fields for maximum type safety
    home_location: Optional[Any] = None        # Provider's home base - Location entity will be imported
    service_areas: List[Any] = field(default_factory=list)              # Service area geofences only (structured objects)
    languages: List[str] = field(default_factory=list)
    transportation: Optional[str] = None
    availability: Optional[ProviderAvailability] = None     # Comprehensive availability schedule
    current_task_count: int = 0
    critical: bool = False

    # Real-time availability status
    current_availability_status: str = "AVAILABLE"  # "AVAILABLE", "UNAVAILABLE", "BUSY", "OFF_DUTY"
    current_unavailable_until: Optional[datetime] = None  # When provider becomes available again

    # Healthcare role specification
    role: Optional[str] = None                      # "RN", "LPN", "CNA", "PT", "OT"
    skills: List[str] = field(default_factory=list)

    # Daily workload limits (structured object)
    capacity: ProviderCapacity = field(default_factory=ProviderCapacity) # Structured capacity constraints

    # Provider preferences (structured object)
    provider_preferences: ProviderPreferences = field(default_factory=ProviderPreferences)

    # Extension point for client-specific attributes during deployment only
    properties: Dict[str, Any] = field(default_factory=dict) 