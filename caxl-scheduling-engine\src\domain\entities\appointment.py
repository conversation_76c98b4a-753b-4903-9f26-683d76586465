"""
Appointment domain entities for healthcare scheduling optimization.
"""

from dataclasses import dataclass, field
from datetime import date, datetime, time
from enum import Enum
from typing import Any, Dict, List, Optional, Annotated
from uuid import UUID

from pydantic import BaseModel, Field
from timefold.solver.domain import (
    planning_entity, planning_solution, PlanningId, PlanningVariable,
    PlanningEntityCollectionProperty, ProblemFactCollectionProperty,
    ValueRangeProvider, PlanningScore
)
from timefold.solver.score import HardSoftScore


class AppointmentStatus(str, Enum):
    """Appointment status tracking through optimization stages."""
    PENDING_TO_ASSIGN = "PENDING_TO_ASSIGN"        # Initial state for new appointments
    ASSIGNED = "ASSIGNED"                          # After assignment solver (provider + date assigned)
    MANUAL_ASSIGNMENT = "MANUAL_ASSIGNMENT"        # User selected alternative assignment
    WAITING_FOR_DAYPLANNER = "WAITING_FOR_DAYPLANNER"  # Sent to day planner
    PLAN_DONE = "PLAN_DONE"                        # Day planning completed
    PENDING_PROVIDER_CONFIRMATION = "PENDING_PROVIDER_CONFIRMATION"  # Awaiting provider confirmation
    PROVIDER_CONFIRMED = "PROVIDER_CONFIRMED"      # Provider confirmed their daily plan
    ROUTE_OPTIMIZED = "ROUTE_OPTIMIZED"            # Route optimization completed
    IN_PROGRESS = "IN_PROGRESS"                    # Provider started the visit
    COMPLETED = "COMPLETED"                        # Visit completed
    CANCELLED = "CANCELLED"                        # Appointment cancelled


class AppointmentPinning(BaseModel):
    """Appointment pinning management for real-time planning."""

    # Pinning flags (prevent re-optimization of completed/in-progress visits)
    is_pinned: bool = False                      # General pinning flag
    pin_provider: bool = False                   # Pin assigned provider (prevent provider reassignment)
    pin_date: bool = False                       # Pin assigned date (prevent date changes)
    pin_time: bool = False                       # Pin assigned time (prevent time changes)
    pin_reason: Optional[str] = None             # Reason for pinning (e.g., "in_progress", "completed", "patient_request")

    def pin_assignment(self, reason: str = "completed") -> None:
        """Pin all aspects of the appointment."""
        self.is_pinned = True
        self.pin_provider = True
        self.pin_date = True
        self.pin_time = True
        self.pin_reason = reason

    def pin_provider_assignment(self, reason: str = "patient_preference") -> None:
        """Pin only the provider assignment."""
        self.pin_provider = True
        self.pin_reason = reason

    def pin_date_assignment(self, reason: str = "patient_request") -> None:
        """Pin only the date assignment."""
        self.pin_date = True
        self.pin_reason = reason

    def pin_time_assignment(self, reason: str = "in_progress") -> None:
        """Pin only the time assignment."""
        self.pin_time = True
        self.pin_reason = reason

    def unpin_assignment(self) -> None:
        """Unpin all aspects of the appointment."""
        self.is_pinned = False
        self.pin_provider = False
        self.pin_date = False
        self.pin_time = False
        self.pin_reason = None

    def unpin_all(self) -> None:
        """Unpin all aspects of the appointment."""
        self.unpin_assignment()

    def is_any_pinned(self) -> bool:
        """Check if any aspect of the appointment is pinned."""
        return self.is_pinned or self.pin_provider or self.pin_date or self.pin_time

    def get_pinning_status(self) -> Dict[str, Any]:
        """Get detailed pinning status for debugging/monitoring."""
        return {
            "is_pinned": self.is_pinned,
            "pin_provider": self.pin_provider,
            "pin_date": self.pin_date,
            "pin_time": self.pin_time,
            "pin_reason": self.pin_reason,
            "any_pinned": self.is_any_pinned()
        }


class AppointmentTiming(BaseModel):
    """Appointment timing constraints and preferences."""

    # Timing constraints - program_type defines behavior
    is_timed_visit: bool = False                 # True for appointments with specific times (e.g., medication at 8 AM)
    preferred_time: Optional[time] = None        # Specific time preference for timed visits
    time_flexibility_minutes: int = 15           # How much the time can vary (±15 minutes)
    earliest_start: Optional[datetime] = None    # Earliest possible start time
    latest_end: Optional[datetime] = None        # Latest possible end time

    def is_flexible(self) -> bool:
        """Check if appointment has flexible timing."""
        return not self.is_timed_visit

    def get_time_window(self, base_time: datetime) -> tuple[datetime, datetime]:
        """Get the acceptable time window around a base time."""
        if self.is_timed_visit and self.preferred_time:
            # For timed visits, use the preferred time with flexibility
            from datetime import timedelta
            start_time = base_time.replace(
                hour=self.preferred_time.hour,
                minute=self.preferred_time.minute
            ) - timedelta(minutes=self.time_flexibility_minutes)
            end_time = base_time.replace(
                hour=self.preferred_time.hour,
                minute=self.preferred_time.minute
            ) + timedelta(minutes=self.time_flexibility_minutes)
            return start_time, end_time
        else:
            # For flexible visits, use the full day
            start_time = base_time.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = base_time.replace(hour=23, minute=59, second=59, microsecond=999999)
            return start_time, end_time


class AppointmentRelationships(BaseModel):
    """Appointment relationships and dependencies."""

    # Relationship fields for episode grouping and dependencies
    care_episode_id: Optional[str] = None        # For grouping related appointments
    related_appointment_ids: List[str] = []      # Generic grouping mechanism (using strings for flexibility)
    prerequisite_appointment_ids: List[str] = [] # Dependencies (using strings for flexibility)
    sequence_order: Optional[int] = None         # Order within group
    same_provider_required: bool = False         # Continuity requirement

    def has_dependencies(self) -> bool:
        """Check if appointment has prerequisite dependencies."""
        return len(self.prerequisite_appointment_ids) > 0

    def has_relationships(self) -> bool:
        """Check if appointment is part of a care episode or has relationships."""
        return (self.care_episode_id is not None or 
                len(self.related_appointment_ids) > 0 or 
                self.sequence_order is not None)

    def is_part_of_sequence(self) -> bool:
        """Check if appointment is part of a sequence."""
        return self.sequence_order is not None


@dataclass
class AppointmentData:
    """Base appointment data shared between assignment and dayplan stages."""
    id: UUID
    consumer_id: UUID
    appointment_date: date
    required_skills: list[str]
    duration_min: int
    urgent: bool = False
    active: bool = True
    status: str = "PENDING_TO_ASSIGN"
    location: Optional[Any] = None  # Location entity will be imported
    priority: str = "normal"
    task_points: Optional[int] = None
    required_role: Optional[str] = None
    timing: AppointmentTiming = field(default_factory=AppointmentTiming)
    relationships: AppointmentRelationships = field(default_factory=AppointmentRelationships)
    pinning: AppointmentPinning = field(default_factory=AppointmentPinning)
    properties: dict = field(default_factory=dict)

    def is_flexible_timing(self) -> bool:
        """Check if appointment has flexible timing."""
        return self.timing.is_flexible()

    def get_time_window(self, base_time: datetime) -> tuple[datetime, datetime]:
        """Get the acceptable time window around a base time."""
        return self.timing.get_time_window(base_time)

    def has_dependencies(self) -> bool:
        """Check if appointment has prerequisite dependencies."""
        return self.relationships.has_dependencies()

    def has_relationships(self) -> bool:
        """Check if appointment is part of a care episode or has relationships."""
        return self.relationships.has_relationships()

    def is_any_pinned(self) -> bool:
        """Check if any aspect of the appointment is pinned."""
        return self.pinning.is_any_pinned()

    def get_pinning_status(self) -> Dict[str, Any]:
        """Get detailed pinning status for debugging/monitoring."""
        return self.pinning.get_pinning_status()


@planning_entity
@dataclass
class AppointmentAssignment:
    """Planning entity representing the assignment of an appointment to a provider and date."""
    id: Annotated[str, PlanningId]
    appointment_data: AppointmentData
    provider: Annotated[Optional[Any], PlanningVariable] = field(default=None)  # Provider entity will be imported
    assigned_date: Annotated[Optional[date], PlanningVariable] = field(default=None)

    def __str__(self):
        if self.provider is None or self.assigned_date is None:
            return f"{self.appointment_data.id} -> unassigned"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}"


@dataclass
class ScheduledAppointment:
    """Appointment that has been assigned a date and provider but needs time assignment."""
    id: str
    appointment_data: AppointmentData
    provider: Any  # Provider entity will be imported
    assigned_date: date
    assigned_time: Optional[time] = None  # Will be assigned by DayPlan job
    
    def __str__(self):
        time_str = f" at {self.assigned_time}" if self.assigned_time else " (time TBD)"
        return f"{self.appointment_data.id} -> {self.provider.name} on {self.assigned_date}{time_str}"


@planning_entity
@dataclass
class TimeSlotAssignment:
    """Planning entity for assigning time slots to scheduled appointments."""
    id: Annotated[str, PlanningId]
    scheduled_appointment: ScheduledAppointment
    time_slot: Annotated[Optional[time], PlanningVariable] = field(default=None)

    def __str__(self):
        if self.time_slot is None:
            return f"{self.scheduled_appointment} -> no time assigned"
        return f"{self.scheduled_appointment.appointment_data.id} -> {self.scheduled_appointment.provider.name} at {self.time_slot}"


@planning_solution
@dataclass
class AppointmentSchedule:
    """Solution representing the complete appointment schedule."""
    id: str
    providers: Annotated[List[Any], ProblemFactCollectionProperty, ValueRangeProvider]  # Provider entities will be imported
    available_dates: Annotated[List[date], ProblemFactCollectionProperty, ValueRangeProvider]
    appointment_assignments: Annotated[List[AppointmentAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)

    def get_provider_assignments(self, provider: Any) -> List[AppointmentAssignment]:
        """Get all appointments assigned to a specific provider."""
        return [assignment for assignment in self.appointment_assignments
                if assignment.provider == provider]

    def get_provider_daily_workload(self, provider: Any, target_date: date) -> int:
        """Calculate provider's workload for a specific date."""
        assignments = self.get_provider_assignments(provider)
        return len([a for a in assignments if a.assigned_date == target_date])


@planning_solution
@dataclass
class DaySchedule:
    """Solution representing the daily time slot assignments."""
    id: str
    date: date
    time_slots: Annotated[List[time], ProblemFactCollectionProperty, ValueRangeProvider]
    scheduled_appointments: Annotated[List[ScheduledAppointment], ProblemFactCollectionProperty]
    time_assignments: Annotated[List[TimeSlotAssignment], PlanningEntityCollectionProperty]
    score: Annotated[Optional[HardSoftScore], PlanningScore] = field(default=None)