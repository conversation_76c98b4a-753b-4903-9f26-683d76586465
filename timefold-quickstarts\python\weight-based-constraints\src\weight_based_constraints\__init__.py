from timefold.solver import SolverFactory
from timefold.solver.config import Sol<PERSON><PERSON>onfig, ScoreDirectorFactoryConfig, TerminationConfig, Duration
from timefold.solver.domain import SolutionManager
import time

from .domain import LoadingSolution, PackageAssignment
from .constraints import define_constraints
from .demo_data import create_demo_data, create_small_demo_data, print_solution_summary


def solve_loading_problem(solution: LoadingSolution, time_limit_seconds: int = 30) -> LoadingSolution:
    """
    Solve the package loading problem using Timefold solver.
    """
    print(f"Solving package loading problem with {len(solution.package_assignments)} packages and {len(solution.containers)} containers...")
    print(f"Time limit: {time_limit_seconds} seconds")
    
    # Create solver configuration
    solver_config = SolverConfig(
        solution_class=LoadingSolution,
        entity_class_list=[PackageAssignment],
        score_director_factory_config=ScoreDirectorFactoryConfig(
            constraint_provider_function=define_constraints
        ),
        termination_config=TerminationConfig(
            spent_limit=Duration(seconds=time_limit_seconds)
        )
    )
    
    # Create solver factory and solver
    solver_factory = SolverFactory.create(solver_config)
    solver = solver_factory.build_solver()
    
    # Solve the problem
    start_time = time.time()
    best_solution = solver.solve(solution)
    end_time = time.time()
    
    print(f"Solving completed in {end_time - start_time:.2f} seconds")
    return best_solution


def main():
    """
    Main function to demonstrate weight-based constraints.
    """
    print("=== Weight-Based Constraints Example ===")
    print("This example demonstrates how to use weight-based constraints in Timefold.")
    print("The problem involves assigning packages to containers while respecting weight and volume constraints.")
    print()
    
    # Create demo data
    print("Creating demo data...")
    solution = create_demo_data()
    
    # Print initial state
    print("Initial solution (all packages unassigned):")
    print_solution_summary(solution)
    
    # Solve the problem
    print("\n" + "="*50)
    print("SOLVING THE PROBLEM")
    print("="*50)
    
    best_solution = solve_loading_problem(solution, time_limit_seconds=30)
    
    # Print final solution
    print("\n" + "="*50)
    print("FINAL SOLUTION")
    print("="*50)
    print_solution_summary(best_solution)
    
    # Analyze the solution
    print("\n" + "="*50)
    print("SOLUTION ANALYSIS")
    print("="*50)
    
    # Check constraint violations
    unassigned = [pa for pa in best_solution.package_assignments if pa.container is None]
    if unassigned:
        print(f"❌ {len(unassigned)} packages remain unassigned")
    else:
        print("✅ All packages assigned")
    
    # Check weight capacity violations
    weight_violations = 0
    for container in best_solution.containers:
        total_weight = best_solution.get_container_total_weight(container)
        if total_weight > container.max_weight:
            weight_violations += 1
            print(f"❌ {container.name} exceeds weight capacity: {total_weight:.1f}kg > {container.max_weight}kg")
    
    if weight_violations == 0:
        print("✅ All containers within weight capacity")
    
    # Check volume capacity violations
    volume_violations = 0
    for container in best_solution.containers:
        total_volume = best_solution.get_container_total_volume(container)
        if total_volume > container.max_volume:
            volume_violations += 1
            print(f"❌ {container.name} exceeds volume capacity: {total_volume:.1f}m³ > {container.max_volume}m³")
    
    if volume_violations == 0:
        print("✅ All containers within volume capacity")
    
    # Show utilization statistics
    print(f"\nContainer Utilization Statistics:")
    total_weight_utilization = 0
    total_volume_utilization = 0
    used_containers = 0
    
    for container in best_solution.containers:
        assignments = best_solution.get_container_assignments(container)
        if assignments:
            used_containers += 1
            weight_util = (best_solution.get_container_total_weight(container) / container.max_weight) * 100
            volume_util = (best_solution.get_container_total_volume(container) / container.max_volume) * 100
            total_weight_utilization += weight_util
            total_volume_utilization += volume_util
            print(f"  {container.name}: Weight {weight_util:.1f}%, Volume {volume_util:.1f}%")
    
    if used_containers > 0:
        avg_weight_util = total_weight_utilization / used_containers
        avg_volume_util = total_volume_utilization / used_containers
        print(f"  Average utilization: Weight {avg_weight_util:.1f}%, Volume {avg_volume_util:.1f}%")


def run_small_example():
    """
    Run a smaller example for quick testing.
    """
    print("=== Small Weight-Based Constraints Example ===")
    
    solution = create_small_demo_data()
    print("Initial solution:")
    print_solution_summary(solution)
    
    best_solution = solve_loading_problem(solution, time_limit_seconds=10)
    
    print("Final solution:")
    print_solution_summary(best_solution)


if __name__ == "__main__":
    main() 