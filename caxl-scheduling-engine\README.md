# CAXL Scheduling Engine

A healthcare appointment scheduling optimization engine built with FastAPI and Timefold solver.

## Overview

The CAXL Scheduling Engine is a job-based optimization system that provides two main optimization stages:

1. **Assignment Job**: Assigns providers and dates to appointments
2. **Day Plan Job**: Optimizes timing and routing within a day

The system runs in daemon mode, allowing API endpoints to invoke optimization jobs and publish results.

## Architecture

The project follows a clean architecture pattern with the following layers:

```
src/
├── application/          # Application layer (use cases)
│   ├── dtos/            # Data Transfer Objects
│   ├── ports/           # Port interfaces
│   ├── services/        # Application services
│   └── usecases/        # Use cases (jobs)
├── domain/              # Domain layer
│   ├── entities/        # Domain entities
│   ├── enums/           # Domain enums
│   ├── models/          # Domain models
│   └── value_objects/   # Value objects
└── infrastructure/      # Infrastructure layer
    ├── api/             # API layer (FastAPI)
    ├── bridges/         # External service bridges
    ├── config/          # Configuration management
    ├── data/            # Data access layer
    └── external/        # External service clients
```

## Features

- **Two-Stage Optimization**: Assignment and Day Planning stages
- **API-First Design**: RESTful API for job invocation
- **Daemon Mode**: Jobs run in background for API consumption
- **Stub Data**: Demo data for testing and development
- **Constraint-Based Optimization**: Timefold solver integration
- **Healthcare-Specific**: Built for healthcare scheduling needs
- **Scenario-Based Testing**: Comprehensive test scenarios for edge cases

## API Endpoints

### Data Fetching
- `GET /appointments` - Fetch all appointments (stub data)
- `GET /providers` - Fetch all providers (stub data)
- `GET /consumers` - Fetch all consumers (stub data)
- `GET /scheduled-appointments/{date}` - Fetch scheduled appointments for a date

### Optimization Jobs
- `POST /assign-appointments` - Run assignment optimization
- `POST /day-plan` - Run day planning optimization

### System
- `GET /` - Root endpoint
- `GET /health` - Health check

## Quick Start with Docker

### Prerequisites
- Docker
- Docker Compose

### Development Mode

1. Clone the repository:
```bash
git clone <repository-url>
cd caxl-scheduling-engine
```

2. Start the development environment:
```bash
docker-compose up -d
```

3. Check the service is running:
```bash
curl http://localhost:8000/health
```

4. Access the API:
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Root: http://localhost:8000/

### Production Mode

```bash
# Build and run production container
docker build -t caxl-scheduling-engine .
docker run -p 8000:8000 caxl-scheduling-engine
```

## Installation (Local Development)

1. Clone the repository:
```bash
git clone <repository-url>
cd caxl-scheduling-engine
```

2. Install dependencies:
```bash
pip install -e .
```

3. Install development dependencies:
```bash
pip install -e ".[dev]"
```

## Usage

### Running the API Server

```bash
# Run the API server
python -m src.main

# Or use the script
api-server
```

The API will be available at `http://localhost:8000`

### Running Jobs

The scheduling engine supports three ways to invoke jobs:

#### 1. Direct Execution
```bash
# Run assignment job directly
python -m src.application.usecases.assign_appointments

# Run day plan job directly
python -m src.application.usecases.day_plan

# Or use the entry point scripts
assign-appointments
day-plan
```

#### 2. Scheduler Once Mode
```bash
# Run assignment job once
python -m src.scheduler --mode once --job assign

# Run day plan job once for today
python -m src.scheduler --mode once --job dayplan

# Run day plan job once for specific date
python -m src.scheduler --mode once --job dayplan --date 2024-01-15

# Or use the scheduler script
scheduler --mode once --job assign
```

#### 3. Scheduled Daemon Mode
```bash
# Run scheduler in daemon mode (runs at 2:00 AM daily for assignment, 6:00 AM for day plan)
python -m src.scheduler --mode daemon

# Or use the scheduler script
scheduler --mode daemon
```

**Schedule:**
- **AssignAppointment job**: Runs nightly at 2:00 AM
- **DayPlan job**: Runs daily at 6:00 AM for the current day

### API Examples

#### Fetch Appointments
```bash
curl http://localhost:8000/appointments
```

#### Run Assignment Optimization
```bash
curl -X POST http://localhost:8000/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{"target_date": "2024-01-15", "service_type": "physical_therapy"}'
```

#### Run Day Planning
```bash
curl -X POST http://localhost:8000/day-plan \
  -H "Content-Type: application/json" \
  -d '{"target_date": "2024-01-15"}'
```

## Docker Commands

### Using Docker Compose

```bash
# Development
docker-compose up -d
docker-compose logs -f
docker-compose down

# Build development image
docker-compose build

# Shell into container
docker-compose exec caxl_scheduling_engine bash
```

### Using Docker Directly

```bash
# Build image
docker build -t caxl-scheduling-engine .

# Run container
docker run -p 8000:8000 caxl-scheduling-engine

# Run with development setup
docker build -f Dockerfile.dev -t caxl-scheduling-engine:dev .
docker run -p 8000:8000 -v $(pwd):/app caxl-scheduling-engine:dev
```

## Configuration

Configuration files are stored in the `config/` directory:

- `scheduler.yml` - Main scheduler configuration
- `physical_therapy.yml` - Physical therapy service configuration
- `skilled_nursing.yml` - Skilled nursing service configuration
- `home_health.yml` - Home health service configuration

## Scenario-Based Testing

The project includes a comprehensive scenario-based testing framework:

### Available Scenarios
- `basic_demo/` - Simple demonstration of core functionality
- `edge_cases/` - Test various edge cases and constraint violations

### Running Scenario Tests

```bash
# Run scenario tests
python test_scenarios.py
```

### Using Scenarios via API

```bash
# List available scenarios
curl http://localhost:8000/scenarios

# Get scenario details
curl http://localhost:8000/scenarios/basic_demo

# Load data with scenario
curl "http://localhost:8000/appointments?scenario=edge_cases"

# Run job with scenario
curl -X POST http://localhost:8000/assign-appointments \
  -H "Content-Type: application/json" \
  -d '{"target_date": "2025-01-15", "scenario": "edge_cases"}'
```

For detailed information, see [Scenario Testing Guide](SCENARIO_TESTING_GUIDE.md).

## Development

### Project Structure

The project follows a clean architecture pattern:

- **Domain Layer**: Core business logic and entities
- **Application Layer**: Use cases and application services
- **Infrastructure Layer**: External concerns (API, data, config)

### Key Components

#### Domain Entities
- `AppointmentData`: Base appointment information
- `Provider`: Healthcare provider information
- `Consumer`: Patient/consumer information
- `Location`: Geographic location data

#### Use Cases
- `AssignAppointmentUseCase`: Assignment optimization
- `DayPlanUseCase`: Day planning optimization

#### Infrastructure
- `ConfigManager`: Configuration management
- `DataLoader`: Data loading (stub implementation)
- `SchedulerBridge`: Constraint definitions

### Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=src

# Run scenario tests
python test_scenarios.py
```

### Code Quality

```bash
# Format code
black src/

# Sort imports
isort src/

# Type checking
mypy src/

# Linting
ruff check src/
```

## Daemon Mode

The system is designed to run in daemon mode, where:

1. The API server starts and initializes use cases in daemon mode
2. API endpoints can invoke optimization jobs
3. Jobs run in the background and publish results
4. Results are available for consumption by external systems

This architecture allows for:
- Scalable job processing
- API-driven optimization
- Real-time job invocation
- Result publishing and consumption

## Stub Data

The system includes comprehensive stub data for testing:

- **Providers**: Physical therapists, nurses, home health aides
- **Consumers**: Patients with various preferences
- **Appointments**: Physical therapy and skilled nursing appointments
- **Locations**: Geographic data for optimization

## Dependencies

### Core Dependencies
- `fastapi`: Web framework
- `timefold`: Optimization solver
- `pydantic`: Data validation
- `loguru`: Logging
- `pyyaml`: Configuration parsing

### Development Dependencies
- `pytest`: Testing framework
- `black`: Code formatting
- `ruff`: Linting
- `mypy`: Type checking

## License

MIT License

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Support

For support and questions, please contact the development team. 