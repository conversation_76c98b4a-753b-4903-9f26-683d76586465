# Weight-Based Constraints Example

This example demonstrates how to implement weight-based constraints in Timefold using Python. The problem involves assigning packages to containers while respecting various weight and volume constraints.

## Problem Description

The goal is to optimally assign packages to containers while considering:

- **Weight constraints**: Each container has a maximum weight capacity
- **Volume constraints**: Each container has a maximum volume capacity  
- **Package characteristics**: Weight, volume, priority, fragility, temperature sensitivity
- **Container characteristics**: Weight/volume capacity, priority level

## Key Features

### Weight-Based Constraints

1. **Hard Constraints** (must be satisfied):
   - All packages must be assigned to a container
   - Fragile packages should not be overloaded with heavy packages
   - Temperature sensitive packages should not be mixed with non-temperature sensitive ones

2. **Soft Constraints** (preferences):
   - Optimize weight distribution across containers
   - Maximize container utilization (weight + volume)
   - Place high priority packages in high priority containers

### Weight-Based Scoring

The example demonstrates several weight-based scoring approaches:

```python
# Weight-based penalty based on excess weight
.penalize(HardSoftScore.ONE_HARD,
          lambda assignment: int(assignment.package.weight * 1.5))

# Weight-based reward for good utilization
.reward(HardSoftScore.ONE_SOFT,
        lambda assignment: int(assignment.package.weight))

# Weight and priority combination scoring
.reward(HardSoftScore.ONE_SOFT,
        lambda assignment: int(assignment.package.weight * assignment.package.priority / 10))
```

## Domain Model

### Container
- `id`: Unique identifier
- `name`: Container name
- `max_weight`: Maximum weight capacity in kg
- `max_volume`: Maximum volume capacity in cubic meters
- `priority`: Priority level (higher = more important)

### Package
- `id`: Unique identifier
- `name`: Package name
- `weight`: Weight in kg
- `volume`: Volume in cubic meters
- `priority`: Priority level (higher = more important)
- `fragility`: Fragility factor (higher = more fragile)
- `temperature_sensitive`: Whether package requires temperature control

### PackageAssignment (Planning Entity)
- `id`: Unique identifier
- `package`: The package to assign
- `container`: The container to assign it to (planning variable)

## Running the Example

### Prerequisites

- Python 3.10 or higher
- Timefold Python library

### Installation

```bash
cd timefold-quickstarts/python/weight-based-constraints
pip install -e .
```

### Running the Application

```bash
# Run the main example
python -m weight_based_constraints

# Or run the small example for quick testing
python -c "from weight_based_constraints import run_small_example; run_small_example()"
```

## Example Output

```
=== Weight-Based Constraints Example ===
This example demonstrates how to use weight-based constraints in Timefold.
The problem involves assigning packages to containers while respecting weight and volume constraints.

Creating demo data...
Initial solution (all packages unassigned):
=== Loading Solution Summary ===
Solution ID: demo-loading-solution
Total Containers: 5
Total Packages: 20

=== Container Utilization ===
Heavy Duty Container:
  Weight: 0.0kg / 1000.0kg (0.0%)
  Volume: 0.0m³ / 50.0m³ (0.0%)
  Packages: 0

...

==================================================
SOLVING THE PROBLEM
==================================================
Solving package loading problem with 20 packages and 5 containers...
Time limit: 30 seconds
Solving completed in 15.23 seconds

==================================================
FINAL SOLUTION
==================================================
=== Loading Solution Summary ===
Solution ID: demo-loading-solution
Total Containers: 5
Total Packages: 20

=== Container Utilization ===
Heavy Duty Container:
  Weight: 800.0kg / 1000.0kg (80.0%)
  Volume: 30.0m³ / 50.0m³ (60.0%)
  Packages: 1
  Package list:
    - Heavy Machinery (800.0kg, 30.0m³, priority:2)

...

==================================================
SOLUTION ANALYSIS
==================================================
✅ All packages assigned
✅ All containers within weight capacity
✅ All containers within volume capacity

Container Utilization Statistics:
  Heavy Duty Container: Weight 80.0%, Volume 60.0%
  Standard Container: Weight 76.0%, Volume 68.0%
  Light Container: Weight 75.0%, Volume 70.0%
  Temperature Controlled: Weight 66.7%, Volume 40.0%
  Fragile Items Only: Weight 80.0%, Volume 75.0%
  Average utilization: Weight 75.5%, Volume 62.6%
```

## Weight-Based Constraint Patterns

### 1. Dynamic Weight Penalties

```python
def weight_capacity_with_dynamic_penalty(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None and
                   assignment.package.weight > assignment.container.max_weight * 0.9)
            .penalize(HardSoftScore.ONE_HARD,
                     lambda assignment: int(assignment.package.weight * 1.5))
            .as_constraint("DynamicWeightPenalty"))
```

### 2. Weight Balance Across Containers

```python
def weight_balance_across_containers(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each_unique_pair(PackageAssignment,
                                 Joiners.equal(lambda assignment: assignment.container))
            .filter(lambda assignment1, assignment2: 
                   assignment1.container is not None and
                   assignment2.container is not None)
            .penalize(HardSoftScore.ONE_SOFT,
                     lambda assignment1, assignment2: 
                     int(abs(assignment1.package.weight - assignment2.package.weight) / 10))
            .as_constraint("WeightBalance"))
```

### 3. Weight and Priority Combination

```python
def weight_priority_scoring(constraint_factory: ConstraintFactory) -> Constraint:
    return (constraint_factory
            .for_each(PackageAssignment)
            .filter(lambda assignment: assignment.container is not None)
            .reward(HardSoftScore.ONE_SOFT,
                   lambda assignment: 
                   int(assignment.package.weight * assignment.package.priority / 10))
            .as_constraint("WeightPriorityScoring"))
```

## Customization

You can customize the example by:

1. **Modifying constraints**: Add new weight-based constraints or modify existing ones
2. **Adjusting weights**: Change the penalty/reward weights in the constraint functions
3. **Adding new characteristics**: Extend the domain model with new package or container properties
4. **Changing demo data**: Modify the demo data to test different scenarios

## Key Learning Points

1. **Weight-based penalties**: Use dynamic penalties based on weight excess
2. **Weight-based rewards**: Reward good weight distribution and utilization
3. **Multi-factor scoring**: Combine weight with other factors like priority and fragility
4. **Constraint filtering**: Use filters to apply constraints only to relevant entities
5. **Hard vs Soft constraints**: Distinguish between must-have (hard) and nice-to-have (soft) constraints

This example provides a foundation for implementing weight-based constraints in various optimization problems using Timefold. 