from .domain import Container, Package, PackageAssignment, LoadingSolution


def create_demo_data() -> LoadingSolution:
    """
    Create demo data for the weight-based constraints example.
    """
    # Create containers with different weight and volume capacities
    containers = [
        Container("C1", "Heavy Duty Container", max_weight=1000.0, max_volume=50.0, priority=3),
        Container("C2", "Standard Container", max_weight=500.0, max_volume=25.0, priority=2),
        Container("C3", "Light Container", max_weight=200.0, max_volume=10.0, priority=1),
        Container("C4", "Temperature Controlled", max_weight=300.0, max_volume=15.0, priority=3),
        Container("C5", "Fragile Items Only", max_weight=150.0, max_volume=8.0, priority=2),
    ]

    # Create packages with different weights, volumes, and characteristics
    packages = [
        # Heavy packages
        Package("P1", "Heavy Machinery", weight=800.0, volume=30.0, priority=2, fragility=1.0),
        Package("P2", "Industrial Equipment", weight=600.0, volume=20.0, priority=2, fragility=1.2),
        Package("P3", "Steel Beams", weight=400.0, volume=15.0, priority=1, fragility=1.0),
        
        # Medium packages
        Package("P4", "Electronics", weight=50.0, volume=2.0, priority=3, fragility=2.5),
        Package("P5", "Medical Supplies", weight=30.0, volume=1.5, priority=4, fragility=1.8),
        Package("P6", "Books", weight=100.0, volume=5.0, priority=1, fragility=1.0),
        Package("P7", "Clothing", weight=80.0, volume=8.0, priority=1, fragility=1.0),
        Package("P8", "Tools", weight=120.0, volume=3.0, priority=2, fragility=1.5),
        
        # Light packages
        Package("P9", "Documents", weight=5.0, volume=0.5, priority=3, fragility=1.0),
        Package("P10", "Jewelry", weight=2.0, volume=0.1, priority=5, fragility=3.0),
        Package("P11", "Artwork", weight=15.0, volume=2.0, priority=4, fragility=2.8),
        Package("P12", "Antiques", weight=25.0, volume=1.0, priority=4, fragility=2.5),
        
        # Temperature sensitive packages
        Package("P13", "Frozen Food", weight=40.0, volume=3.0, priority=3, fragility=1.5, temperature_sensitive=True),
        Package("P14", "Pharmaceuticals", weight=20.0, volume=1.0, priority=4, fragility=1.8, temperature_sensitive=True),
        Package("P15", "Flowers", weight=10.0, volume=2.0, priority=2, fragility=2.0, temperature_sensitive=True),
        
        # Fragile packages
        Package("P16", "Glass Items", weight=30.0, volume=1.5, priority=3, fragility=3.0),
        Package("P17", "Ceramics", weight=25.0, volume=1.0, priority=3, fragility=2.8),
        Package("P18", "Mirrors", weight=40.0, volume=2.0, priority=2, fragility=3.0),
        
        # Regular packages
        Package("P19", "Household Items", weight=60.0, volume=4.0, priority=1, fragility=1.0),
        Package("P20", "Sports Equipment", weight=70.0, volume=6.0, priority=1, fragility=1.2),
    ]

    # Create package assignments (initially unassigned)
    package_assignments = [
        PackageAssignment(f"PA{i+1}", package) 
        for i, package in enumerate(packages)
    ]

    return LoadingSolution(
        id="demo-loading-solution",
        containers=containers,
        package_assignments=package_assignments
    )


def create_small_demo_data() -> LoadingSolution:
    """
    Create a smaller demo dataset for testing.
    """
    containers = [
        Container("C1", "Heavy Container", max_weight=500.0, max_volume=20.0, priority=2),
        Container("C2", "Light Container", max_weight=200.0, max_volume=10.0, priority=1),
        Container("C3", "Fragile Container", max_weight=100.0, max_volume=5.0, priority=3),
    ]

    packages = [
        Package("P1", "Heavy Item", weight=300.0, volume=12.0, priority=2, fragility=1.0),
        Package("P2", "Fragile Item", weight=20.0, volume=1.0, priority=3, fragility=2.5),
        Package("P3", "Light Item", weight=50.0, volume=3.0, priority=1, fragility=1.0),
        Package("P4", "Medium Item", weight=100.0, volume=5.0, priority=2, fragility=1.5),
        Package("P5", "Very Fragile", weight=10.0, volume=0.5, priority=4, fragility=3.0),
    ]

    package_assignments = [
        PackageAssignment(f"PA{i+1}", package) 
        for i, package in enumerate(packages)
    ]

    return LoadingSolution(
        id="small-demo-solution",
        containers=containers,
        package_assignments=package_assignments
    )


def print_solution_summary(solution: LoadingSolution):
    """
    Print a summary of the loading solution.
    """
    print(f"\n=== Loading Solution Summary ===")
    print(f"Solution ID: {solution.id}")
    print(f"Total Containers: {len(solution.containers)}")
    print(f"Total Packages: {len(solution.package_assignments)}")
    
    print(f"\n=== Container Utilization ===")
    for container in solution.containers:
        assignments = solution.get_container_assignments(container)
        total_weight = solution.get_container_total_weight(container)
        total_volume = solution.get_container_total_volume(container)
        weight_utilization = (total_weight / container.max_weight) * 100
        volume_utilization = (total_volume / container.max_volume) * 100
        
        print(f"{container.name}:")
        print(f"  Weight: {total_weight:.1f}kg / {container.max_weight}kg ({weight_utilization:.1f}%)")
        print(f"  Volume: {total_volume:.1f}m³ / {container.max_volume}m³ ({volume_utilization:.1f}%)")
        print(f"  Packages: {len(assignments)}")
        
        if assignments:
            print(f"  Package list:")
            for assignment in assignments:
                package = assignment.package
                print(f"    - {package.name} ({package.weight}kg, {package.volume}m³, priority:{package.priority})")
        print()
    
    # Check for unassigned packages
    unassigned = [pa for pa in solution.package_assignments if pa.container is None]
    if unassigned:
        print(f"Unassigned packages ({len(unassigned)}):")
        for assignment in unassigned:
            package = assignment.package
            print(f"  - {package.name} ({package.weight}kg, {package.volume}m³)")
    
    if solution.score:
        print(f"\nSolution Score: {solution.score}") 