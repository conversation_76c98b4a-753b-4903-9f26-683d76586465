<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="content-type" content="text/html; charset=UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <title>Facility location - Timefold Solver on Quarkus</title>
  <link rel="stylesheet" href="/webjars/bootstrap/css/bootstrap.min.css">
  <link rel="stylesheet" href="/webjars/leaflet/leaflet.css">
  <link rel="stylesheet" href="/webjars/font-awesome/css/all.min.css">
  <link rel="stylesheet" href="/webjars/timefold/css/timefold-webui.css" />
  <link rel="icon" href="/webjars/timefold/img/timefold-favicon.svg" type="image/svg+xml">
</head>
<body>

<header id="timefold-auto-header"></header>
<div class="sticky-top d-flex justify-content-center align-items-center">
  <div id="notificationPanel" style="position: absolute; top: .5rem;"></div>
</div>

<div class="container-fluid">
  <div class="row">
    <div class="col-7 col-lg-8 col-xl-9">
      <div id="map" style="width: 100%; height: 100vh;"></div>
    </div>
    <div class="col-5 col-lg-4 col-xl-3" style="height: 100vh; overflow-y: scroll;">
      <div class="row pt-2 row-cols-1">
        <div class="col mb-3">
          <button id="solveButton" type="button" class="btn btn-success">
            <i class="fas fa-play"></i> Solve
          </button>
          <button id="stopSolvingButton" type="button" class="btn btn-danger">
            <i class="fas fa-stop"></i> Stop solving
          </button>
        </div>
        <div class="col">
          <h5>
            Solution summary
            <a id="analyzeButton" class="float-justify" href="#" role="button">
              <i class="fas fa-info-circle"></i>
            </a>
          </h5>
          <table class="table">
            <tr>
              <td>Score:</td>
              <td><span id="score">unknown</span></td>
            </tr>
            <tr>
              <td>Total cost:</td>
              <td><span id="cost">unknown</span> (<span id="cost-percentage">unknown</span>%)</td>
            </tr>
            <tr>
              <td>Total distance:</td>
              <td><span id="distance">unknown</span></td>
            </tr>
          </table>
        </div>
        <div class="col">
          <h5>Facility list</h5>
          <table class="table-sm w-100">
            <thead>
            <tr>
              <th style="width: 1rem"></th>
              <th>Name</th>
              <th>Usage</th>
              <th class="text-end">Setup cost</th>
            </tr>
            </thead>
            <tbody id="facilities"></tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<footer id="timefold-auto-footer"></footer>
<div class="modal fadebd-example-modal-lg" id="scoreAnalysisModal" tabindex="-1"
     aria-labelledby="scoreAnalysisModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h1 class="modal-title fs-5" id="scoreAnalysisModalLabel">Score analysis <span
                id="scoreAnalysisScoreLabel"></span></h1>

        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="scoreAnalysisModalContent">
        <!-- Filled in by app.js -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

<script src="/webjars/leaflet/leaflet.js"></script>
<script src="/webjars/jquery/jquery.min.js"></script>
<script src="/webjars/bootstrap/js/bootstrap.bundle.min.js"></script>
<script src="/webjars/timefold/js/timefold-webui.js"></script>
<script src="/app.js"></script>
</body>
</html>
