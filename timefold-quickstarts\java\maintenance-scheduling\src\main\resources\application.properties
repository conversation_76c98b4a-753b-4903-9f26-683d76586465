########################
# Timefold Solver properties
########################

# The solver runs for 30 seconds. To run for 5 minutes use "5m" and for 2 hours use "2h".
quarkus.timefold.solver.termination.spent-limit=30s

# To change how many solvers to run in parallel
# timefold.solver-manager.parallel-solver-count=4

# Temporary comment this out to detect bugs in your code (lowers performance)
# quarkus.timefold.solver.environment-mode=FULL_ASSERT

# Temporary comment this out to return a feasible solution as soon as possible
# quarkus.timefold.solver.termination.best-score-limit=0hard/*soft

# To see what Timefold is doing, turn on DEBUG or TRACE logging.
quarkus.log.category."ai.timefold.solver".level=INFO
%test.quarkus.log.category."ai.timefold.solver".level=INFO
%prod.quarkus.log.category."ai.timefold.solver".level=INFO

# XML file for power tweaking, defaults to solverConfig.xml (directly under src/main/resources)
# quarkus.timefold.solver-config-xml=org/.../maintenanceScheduleSolverConfig.xml

########################
# Timefold Solver Enterprise properties
########################

# To run increase CPU cores usage per solver
%enterprise.quarkus.timefold.solver.move-thread-count=AUTO

########################
# Native build properties
########################

# Enable Swagger UI also in the native mode
quarkus.swagger-ui.always-include=true

########################
# Test overrides
########################

# Effectively disable spent-time termination in favor of the best-score-limit
%test.quarkus.timefold.solver.termination.spent-limit=1h
%test.quarkus.timefold.solver.termination.best-score-limit=0hard/*soft