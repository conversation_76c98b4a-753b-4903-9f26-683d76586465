"""
Location value objects for healthcare scheduling optimization.
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class Location(BaseModel):
    """Geographic point location - maps to GEOMETRY(POINT, 4326) in PostgreSQL."""
    latitude: float                              # Y coordinate
    longitude: float                             # X coordinate
    address: Optional[str] = None                # Human-readable address
    city: Optional[str] = None                   # City name
    state: Optional[str] = None                  # State/province
    country: Optional[str] = None                # Country
    zip_code: Optional[str] = None               # Postal code


class Geofence(BaseModel):
    """Geofence with boundary data for optimization-time spatial calculations."""
    id: Optional[int] = None                     # Database ID reference
    name: Optional[str] = None                   # "Brooklyn Heights", "Unsafe Zone 1"
    boundary_wkt: Optional[str] = None           # WKT format: "POLYGON((-73.9961 40.6955, ...))"
    zone_type: str                               # "service" or "blackout"
    description: Optional[str] = None            # Human-readable description

    # Service geofence fields (when zone_type = "service")
    priority: Optional[int] = None               # Priority if overlapping (1=highest)

    # Blackout geofence fields (when zone_type = "blackout")
    reason: Optional[str] = None                 # "unsafe_area", "no_parking", "traffic_congestion"
    severity: str = "hard"                       # "hard", "soft", "time_based" 