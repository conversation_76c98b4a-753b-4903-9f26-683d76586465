"""
FastAPI application for the healthcare scheduling optimization service.

This module provides REST API endpoints for appointment assignment and day planning.
"""

from datetime import date, datetime
from typing import List, Dict, Any, Optional
from uuid import UUID

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from loguru import logger

from src.application.usecases.assign_appointments import Assignment<PERSON>ob
from src.application.usecases.day_plan import Day<PERSON><PERSON><PERSON><PERSON>
from src.infrastructure.data.data_loader import (
    create_demo_data, create_demo_scheduled_appointments,
    get_available_scenarios, get_scenario_info
)
from src.infrastructure.config.config_manager import get_config


# Pydantic models for API requests/responses
class AssignmentRequest(BaseModel):
    target_date: Optional[date] = None
    scenario: Optional[str] = None
    config_overrides: Optional[Dict[str, Any]] = None


class DayPlanRequest(BaseModel):
    target_date: date
    scenario: Optional[str] = None
    config_overrides: Optional[Dict[str, Any]] = None


class JobResponse(BaseModel):
    job_id: str
    status: str
    message: str
    target_date: Optional[date] = None
    scenario: Optional[str] = None


class AppointmentResponse(BaseModel):
    id: str
    consumer_id: str
    appointment_date: date
    required_skills: List[str]
    duration_min: int
    urgent: bool
    status: str
    location: Optional[Dict[str, Any]] = None


class ProviderResponse(BaseModel):
    id: str
    name: str
    role: Optional[str] = None
    skills: List[str]
    home_location: Optional[Dict[str, Any]] = None


class ConsumerResponse(BaseModel):
    id: str
    name: str
    location: Optional[Dict[str, Any]] = None
    care_episode_id: Optional[str] = None


class ScheduledAppointmentResponse(BaseModel):
    id: str
    appointment: AppointmentResponse
    provider: ProviderResponse
    assigned_date: date
    assigned_time: Optional[str] = None


class ScenarioInfoResponse(BaseModel):
    name: str
    path: str
    exists: bool
    files: List[str]
    has_readme: bool


# Create FastAPI app
app = FastAPI(
    title="CAXL Scheduling Engine",
    description="Healthcare appointment scheduling optimization service",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Background job storage (in production, use Redis or database)
background_jobs: Dict[str, Dict[str, Any]] = {}


@app.get("/")
async def root():
    """Root endpoint with service information."""
    return {
        "service": "CAXL Scheduling Engine",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "scenarios": "/scenarios",
            "appointments": "/appointments",
            "providers": "/providers",
            "consumers": "/consumers",
            "scheduled": "/scheduled/{date}",
            "assign": "/assign-appointments",
            "day-plan": "/day-plan"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}


@app.get("/scenarios", response_model=List[str])
async def list_scenarios():
    """Get list of available test scenarios."""
    try:
        scenarios = get_available_scenarios()
        logger.info(f"Available scenarios: {scenarios}")
        return scenarios
    except Exception as e:
        logger.error(f"Error listing scenarios: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing scenarios: {str(e)}")


@app.get("/scenarios/{scenario_name}", response_model=ScenarioInfoResponse)
async def get_scenario_details(scenario_name: str):
    """Get detailed information about a specific scenario."""
    try:
        info = get_scenario_info(scenario_name)
        logger.info(f"Scenario info for {scenario_name}: {info}")
        return ScenarioInfoResponse(**info)
    except Exception as e:
        logger.error(f"Error getting scenario info for {scenario_name}: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scenario info: {str(e)}")


@app.get("/appointments", response_model=List[AppointmentResponse])
async def get_appointments(scenario: Optional[str] = None):
    """Get appointments data (demo or scenario-based)."""
    try:
        demo_data = create_demo_data(scenario)
        appointments = demo_data["appointments"]
        
        # Convert to response format
        response_appointments = []
        for apt in appointments:
            location_dict = None
            if apt.location:
                location_dict = {
                    "latitude": apt.location.latitude,
                    "longitude": apt.location.longitude,
                    "address": apt.location.address,
                    "city": apt.location.city,
                    "state": apt.location.state
                }
            
            response_appointments.append(AppointmentResponse(
                id=str(apt.id),
                consumer_id=str(apt.consumer_id),
                appointment_date=apt.appointment_date,
                required_skills=apt.required_skills,
                duration_min=apt.duration_min,
                urgent=apt.urgent,
                status=apt.status,
                location=location_dict
            ))
        
        logger.info(f"Retrieved {len(response_appointments)} appointments (scenario: {scenario})")
        return response_appointments
    except Exception as e:
        logger.error(f"Error getting appointments: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting appointments: {str(e)}")


@app.get("/providers", response_model=List[ProviderResponse])
async def get_providers(scenario: Optional[str] = None):
    """Get providers data (demo or scenario-based)."""
    try:
        demo_data = create_demo_data(scenario)
        providers = demo_data["providers"]
        
        # Convert to response format
        response_providers = []
        for provider in providers:
            location_dict = None
            if provider.home_location:
                location_dict = {
                    "latitude": provider.home_location.latitude,
                    "longitude": provider.home_location.longitude,
                    "address": provider.home_location.address,
                    "city": provider.home_location.city,
                    "state": provider.home_location.state
                }
            
            response_providers.append(ProviderResponse(
                id=str(provider.id),
                name=provider.name,
                role=provider.role,
                skills=provider.skills,
                home_location=location_dict
            ))
        
        logger.info(f"Retrieved {len(response_providers)} providers (scenario: {scenario})")
        return response_providers
    except Exception as e:
        logger.error(f"Error getting providers: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting providers: {str(e)}")


@app.get("/consumers", response_model=List[ConsumerResponse])
async def get_consumers(scenario: Optional[str] = None):
    """Get consumers data (demo or scenario-based)."""
    try:
        demo_data = create_demo_data(scenario)
        consumers = demo_data["consumers"]
        
        # Convert to response format
        response_consumers = []
        for consumer in consumers:
            location_dict = None
            if consumer.location:
                location_dict = {
                    "latitude": consumer.location.latitude,
                    "longitude": consumer.location.longitude,
                    "address": consumer.location.address,
                    "city": consumer.location.city,
                    "state": consumer.location.state
                }
            
            response_consumers.append(ConsumerResponse(
                id=str(consumer.id),
                name=consumer.name,
                location=location_dict,
                care_episode_id=consumer.care_episode_id
            ))
        
        logger.info(f"Retrieved {len(response_consumers)} consumers (scenario: {scenario})")
        return response_consumers
    except Exception as e:
        logger.error(f"Error getting consumers: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting consumers: {str(e)}")


@app.get("/scheduled/{target_date}", response_model=List[ScheduledAppointmentResponse])
async def get_scheduled_appointments(target_date: date, scenario: Optional[str] = None):
    """Get scheduled appointments for a specific date."""
    try:
        scheduled_appointments = create_demo_scheduled_appointments(target_date, scenario)
        
        # Convert to response format
        response_scheduled = []
        for scheduled in scheduled_appointments:
            # Convert appointment
            apt_location = None
            if scheduled.appointment_data.location:
                apt_location = {
                    "latitude": scheduled.appointment_data.location.latitude,
                    "longitude": scheduled.appointment_data.location.longitude,
                    "address": scheduled.appointment_data.location.address,
                    "city": scheduled.appointment_data.location.city,
                    "state": scheduled.appointment_data.location.state
                }
            
            appointment_response = AppointmentResponse(
                id=str(scheduled.appointment_data.id),
                consumer_id=str(scheduled.appointment_data.consumer_id),
                appointment_date=scheduled.appointment_data.appointment_date,
                required_skills=scheduled.appointment_data.required_skills,
                duration_min=scheduled.appointment_data.duration_min,
                urgent=scheduled.appointment_data.urgent,
                status=scheduled.appointment_data.status,
                location=apt_location
            )
            
            # Convert provider
            provider_location = None
            if scheduled.provider.home_location:
                provider_location = {
                    "latitude": scheduled.provider.home_location.latitude,
                    "longitude": scheduled.provider.home_location.longitude,
                    "address": scheduled.provider.home_location.address,
                    "city": scheduled.provider.home_location.city,
                    "state": scheduled.provider.home_location.state
                }
            
            provider_response = ProviderResponse(
                id=str(scheduled.provider.id),
                name=scheduled.provider.name,
                role=scheduled.provider.role,
                skills=scheduled.provider.skills,
                home_location=provider_location
            )
            
            response_scheduled.append(ScheduledAppointmentResponse(
                id=scheduled.id,
                appointment=appointment_response,
                provider=provider_response,
                assigned_date=scheduled.assigned_date,
                assigned_time=scheduled.assigned_time.isoformat() if scheduled.assigned_time else None
            ))
        
        logger.info(f"Retrieved {len(response_scheduled)} scheduled appointments for {target_date} (scenario: {scenario})")
        return response_scheduled
    except Exception as e:
        logger.error(f"Error getting scheduled appointments: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting scheduled appointments: {str(e)}")


@app.post("/assign-appointments", response_model=JobResponse)
async def assign_appointments(request: AssignmentRequest, background_tasks: BackgroundTasks):
    """Run appointment assignment job in background."""
    try:
        job_id = f"assign_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        target_date = request.target_date or date.today()
        
        # Store job info
        background_jobs[job_id] = {
            "type": "assignment",
            "status": "running",
            "target_date": target_date,
            "scenario": request.scenario,
            "start_time": datetime.now()
        }
        
        # Run job in background
        background_tasks.add_task(
            run_assignment_job,
            job_id,
            target_date,
            request.scenario,
            request.config_overrides
        )
        
        logger.info(f"Started assignment job {job_id} for {target_date} (scenario: {request.scenario})")
        
        return JobResponse(
            job_id=job_id,
            status="running",
            message="Assignment job started",
            target_date=target_date,
            scenario=request.scenario
        )
    except Exception as e:
        logger.error(f"Error starting assignment job: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting assignment job: {str(e)}")


@app.post("/day-plan", response_model=JobResponse)
async def create_day_plan(request: DayPlanRequest, background_tasks: BackgroundTasks):
    """Run day planning job in background."""
    try:
        job_id = f"dayplan_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Store job info
        background_jobs[job_id] = {
            "type": "day_plan",
            "status": "running",
            "target_date": request.target_date,
            "scenario": request.scenario,
            "start_time": datetime.now()
        }
        
        # Run job in background
        background_tasks.add_task(
            run_day_plan_job,
            job_id,
            request.target_date,
            request.scenario,
            request.config_overrides
        )
        
        logger.info(f"Started day plan job {job_id} for {request.target_date} (scenario: {request.scenario})")
        
        return JobResponse(
            job_id=job_id,
            status="running",
            message="Day plan job started",
            target_date=request.target_date,
            scenario=request.scenario
        )
    except Exception as e:
        logger.error(f"Error starting day plan job: {e}")
        raise HTTPException(status_code=500, detail=f"Error starting day plan job: {str(e)}")


@app.get("/jobs/{job_id}")
async def get_job_status(job_id: str):
    """Get status of a background job."""
    if job_id not in background_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    job_info = background_jobs[job_id]
    return {
        "job_id": job_id,
        "type": job_info["type"],
        "status": job_info["status"],
        "target_date": job_info.get("target_date"),
        "scenario": job_info.get("scenario"),
        "start_time": job_info["start_time"].isoformat(),
        "end_time": job_info.get("end_time", ""),
        "result": job_info.get("result", ""),
        "error": job_info.get("error", "")
    }


async def run_assignment_job(job_id: str, target_date: date, scenario: Optional[str], config_overrides: Optional[Dict[str, Any]]):
    """Run assignment job in background."""
    try:
        logger.info(f"Running assignment job {job_id} for {target_date} (scenario: {scenario})")
        
        # Apply config overrides if provided
        if config_overrides:
            # This would update the config manager
            logger.info(f"Applying config overrides: {config_overrides}")
        
        # Create and run assignment job
        job = AssignmentJob()
        result = await job.run(target_date, scenario)
        
        # Update job status
        background_jobs[job_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "result": result
        })
        
        logger.info(f"Assignment job {job_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Assignment job {job_id} failed: {e}")
        background_jobs[job_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error": str(e)
        })


async def run_day_plan_job(job_id: str, target_date: date, scenario: Optional[str], config_overrides: Optional[Dict[str, Any]]):
    """Run day plan job in background."""
    try:
        logger.info(f"Running day plan job {job_id} for {target_date} (scenario: {scenario})")
        
        # Apply config overrides if provided
        if config_overrides:
            # This would update the config manager
            logger.info(f"Applying config overrides: {config_overrides}")
        
        # Create and run day plan job
        job = DayPlanJob()
        result = await job.run(target_date, scenario)
        
        # Update job status
        background_jobs[job_id].update({
            "status": "completed",
            "end_time": datetime.now(),
            "result": result
        })
        
        logger.info(f"Day plan job {job_id} completed successfully")
        
    except Exception as e:
        logger.error(f"Day plan job {job_id} failed: {e}")
        background_jobs[job_id].update({
            "status": "failed",
            "end_time": datetime.now(),
            "error": str(e)
        })


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 