"""
Configuration manager for healthcare scheduling optimization.
"""

import os
import yaml
from typing import Dict, Any, Optional, List
from pathlib import Path

from loguru import logger

from ...domain.models.config import SchedulerConfig, ServiceConfig


class ConfigManager:
    """Manages configuration for the scheduling engine."""
    
    def __init__(self, config_folder: str = "config"):
        """Initialize the configuration manager."""
        self.config_folder = config_folder
        self._scheduler_config: Optional[SchedulerConfig] = None
        self._service_configs: Dict[str, ServiceConfig] = {}
        self._load_configs()
    
    def _load_configs(self):
        """Load all configuration files."""
        config_path = Path(self.config_folder)
        
        if not config_path.exists():
            logger.warning(f"Config folder {self.config_folder} does not exist, using defaults")
            self._scheduler_config = SchedulerConfig()
            return
        
        # Load scheduler config
        scheduler_config_path = config_path / "scheduler.yml"
        if scheduler_config_path.exists():
            with open(scheduler_config_path, 'r') as f:
                scheduler_data = yaml.safe_load(f)
                self._scheduler_config = SchedulerConfig(**scheduler_data)
        else:
            logger.warning("scheduler.yml not found, using defaults")
            self._scheduler_config = SchedulerConfig()
        
        # Load service configs
        service_configs = ["physical_therapy.yml", "skilled_nursing.yml", "home_health.yml"]
        for service_config_file in service_configs:
            service_config_path = config_path / service_config_file
            if service_config_path.exists():
                with open(service_config_path, 'r') as f:
                    service_data = yaml.safe_load(f)
                    service_type = service_data.get('service_type', service_config_file.replace('.yml', ''))
                    self._service_configs[service_type] = ServiceConfig(**service_data)
                    logger.info(f"Loaded service config: {service_type}")
            else:
                logger.warning(f"Service config {service_config_file} not found")
    
    def get_scheduler_config(self) -> SchedulerConfig:
        """Get the main scheduler configuration."""
        if self._scheduler_config is None:
            self._scheduler_config = SchedulerConfig()
        return self._scheduler_config
    
    def get_service_config(self, service_type: str) -> Optional[ServiceConfig]:
        """Get configuration for a specific service type."""
        return self._service_configs.get(service_type)
    
    def get_all_service_configs(self) -> Dict[str, ServiceConfig]:
        """Get all service configurations."""
        return self._service_configs.copy()
    
    def validate_configs(self) -> List[str]:
        """Validate all configurations and return any issues."""
        issues = []
        
        if not self._scheduler_config:
            issues.append("No scheduler configuration loaded")
        
        if not self._service_configs:
            issues.append("No service configurations loaded")
        
        # Validate scheduler config
        if self._scheduler_config:
            if self._scheduler_config.rolling_window_days <= 0:
                issues.append("rolling_window_days must be positive")
            
            if self._scheduler_config.max_solving_time_seconds <= 0:
                issues.append("max_solving_time_seconds must be positive")
        
        # Validate service configs
        for service_type, config in self._service_configs.items():
            if not config.required_skills:
                issues.append(f"Service {service_type} has no required skills")
            
            if config.geographic_radius_miles <= 0:
                issues.append(f"Service {service_type} has invalid geographic radius")
        
        return issues
    
    def reload_configs(self):
        """Reload all configuration files."""
        logger.info("Reloading configurations...")
        self._scheduler_config = None
        self._service_configs.clear()
        self._load_configs()
        logger.info("Configurations reloaded") 