"""
Basic tests for the CAXL Scheduling Engine.
"""

import pytest
from datetime import date

from src.infrastructure.data.data_loader import create_demo_data
from src.infrastructure.config.config_manager import ConfigManager


def test_demo_data_creation():
    """Test that demo data can be created."""
    demo_data = create_demo_data()
    
    assert "providers" in demo_data
    assert "consumers" in demo_data
    assert "appointments" in demo_data
    
    assert len(demo_data["providers"]) > 0
    assert len(demo_data["consumers"]) > 0
    assert len(demo_data["appointments"]) > 0


def test_config_manager():
    """Test that configuration manager works."""
    config_manager = ConfigManager()
    
    scheduler_config = config_manager.get_scheduler_config()
    assert scheduler_config is not None
    assert scheduler_config.rolling_window_days == 7
    
    service_configs = config_manager.get_all_service_configs()
    assert len(service_configs) > 0


def test_api_imports():
    """Test that API components can be imported."""
    try:
        from src.infrastructure.api.app import app
        assert app is not None
    except ImportError as e:
        pytest.skip(f"API imports not available: {e}")


def test_domain_entities():
    """Test that domain entities can be imported."""
    try:
        from src.domain.entities.appointment import AppointmentData
        from src.domain.entities.provider import Provider
        from src.domain.entities.consumer import Consumer
        from src.domain.value_objects.location import Location
        
        assert AppointmentData is not None
        assert Provider is not None
        assert Consumer is not None
        assert Location is not None
    except ImportError as e:
        pytest.skip(f"Domain entity imports not available: {e}")


if __name__ == "__main__":
    pytest.main([__file__]) 