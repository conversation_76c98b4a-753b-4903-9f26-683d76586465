# Quick Start Guide - Weight-Based Constraints

This guide shows you how to quickly get started with the weight-based constraints example.

## What You'll Learn

This example demonstrates how to implement weight-based constraints in Timefold using Python. You'll learn:

- How to create constraints that consider package weights
- How to implement dynamic penalties based on weight excess
- How to optimize weight distribution across containers
- How to combine weight with other factors like priority and fragility

## Problem Scenario

You have packages of different weights, volumes, and characteristics that need to be assigned to containers with weight and volume capacity limits. The goal is to:

1. Assign all packages to containers
2. Respect weight and volume constraints
3. Optimize container utilization
4. Consider package characteristics (fragility, temperature sensitivity, priority)

## Quick Setup

### 1. Install Dependencies

```bash
cd timefold-quickstarts/python/weight-based-constraints
pip install timefold==1.23.0b0
```

### 2. Run the Example

```bash
# Option 1: Run the interactive example
python example.py

# Option 2: Run directly as a module
python -m weight_based_constraints

# Option 3: Run the small example for quick testing
python -c "import sys; sys.path.insert(0, 'src'); from weight_based_constraints import run_small_example; run_small_example()"
```

## Key Weight-Based Constraint Patterns

### 1. Weight Capacity Constraints

```python
# Hard constraint: Don't exceed container weight capacity
.penalize(HardSoftScore.ONE_HARD,
          lambda assignment: int(assignment.package.weight * 1.5))
```

### 2. Weight Distribution Optimization

```python
# Soft constraint: Balance weight across containers
.penalize(HardSoftScore.ONE_SOFT,
          lambda assignment1, assignment2: 
          int(abs(assignment1.package.weight - assignment2.package.weight) / 10))
```

### 3. Weight and Priority Combination

```python
# Soft constraint: Reward based on weight and priority
.reward(HardSoftScore.ONE_SOFT,
        lambda assignment: 
        int(assignment.package.weight * assignment.package.priority / 10))
```

## Example Output

When you run the example, you'll see output like this:

```
=== Weight-Based Constraints Example ===
Solving package loading problem with 20 packages and 5 containers...
Time limit: 30 seconds
Solving completed in 15.23 seconds

=== Container Utilization ===
Heavy Duty Container:
  Weight: 800.0kg / 1000.0kg (80.0%)
  Volume: 30.0m³ / 50.0m³ (60.0%)
  Packages: 1

=== Solution Analysis ===
✅ All packages assigned
✅ All containers within weight capacity
✅ All containers within volume capacity
```

## Customization Ideas

1. **Add new weight-based constraints**:
   - Penalize containers that are too light (inefficient)
   - Reward balanced weight distribution
   - Consider weight stability during transport

2. **Modify scoring weights**:
   - Adjust penalty multipliers for different constraint violations
   - Change reward weights for optimization goals

3. **Extend the domain model**:
   - Add package dimensions (length, width, height)
   - Include container stacking constraints
   - Add delivery time windows

## Troubleshooting

### Common Issues

1. **Import errors**: Make sure you're in the correct directory and Timefold is installed
2. **Solver not converging**: Try increasing the time limit or adjusting constraint weights
3. **Memory issues**: Use the small example for testing

### Getting Help

- Check the main README.md for detailed documentation
- Look at the constraint patterns in `src/weight_based_constraints/constraints.py`
- Examine the domain model in `src/weight_based_constraints/domain.py`

## Next Steps

After running this example, try:

1. Modifying the constraints to add your own weight-based rules
2. Creating your own domain model with weight considerations
3. Exploring other Timefold examples for different constraint patterns
4. Implementing weight-based constraints in your own optimization problems

This example provides a solid foundation for understanding how to implement weight-based constraints in Timefold! 