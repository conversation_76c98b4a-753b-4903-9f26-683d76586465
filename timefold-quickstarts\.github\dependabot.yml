version: 2
updates:
  - package-ecosystem: maven
    target-branch: "development"
    directory: "/"
    schedule:
      interval: weekly
      time: '05:00' # Otherwise it picks a random time.
    open-pull-requests-limit: 10
    commit-message:
      prefix: "deps: "
  - package-ecosystem: gradle
    target-branch: "development"
    directory: "/"
    schedule:
      interval: weekly
      time: '05:00' # Otherwise it picks a random time.
    open-pull-requests-limit: 10
    commit-message:
      prefix: "deps: "
  - package-ecosystem: "github-actions"
    target-branch: "development"
    directory: "/"
    schedule:
      interval: weekly
      time: '05:00' # Otherwise it picks a random time.
    commit-message:
      prefix: "deps: "
