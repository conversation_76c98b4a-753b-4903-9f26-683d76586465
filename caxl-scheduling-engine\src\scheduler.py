#!/usr/bin/env python3
"""
Scheduler for running appointment scheduling jobs.

This module provides a scheduler that can run both:
1. AssignAppointment job (nightly) - assigns date and provider
2. DayPlan job (daily) - assigns time slots to daily appointments
"""

import argparse
import schedule
import sys
import threading
import time
from datetime import date, datetime, timedelta
from typing import Optional, Union

from loguru import logger

from .infrastructure.config.config_manager import ConfigManager
from .application.usecases.assign_appointments import AssignAppointmentUseCase
from .application.usecases.day_plan import DayPlanUseCase


class AppointmentScheduler:
    """Main scheduler for appointment scheduling jobs."""
    
    def __init__(self, config_folder: str = "config", daemon_mode: bool = False):
        self.config_manager = ConfigManager(config_folder)
        self.scheduler_config = self.config_manager.get_scheduler_config()
        self.assign_job = AssignAppointmentUseCase(self.config_manager, daemon_mode=daemon_mode)
        self.day_plan_job = DayPlanUseCase(self.config_manager, daemon_mode=daemon_mode)
        self.daemon_mode = daemon_mode
        self._setup_logging()
        self._running = False
    
    def _setup_logging(self):
        """Setup logging configuration."""
        logger.remove()  # Remove default handler
        logger.add(
            "logs/scheduler_{time}.log",
            rotation="1 day",
            retention="30 days",
            level=self.scheduler_config.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
        )
        logger.add(
            lambda msg: print(msg, end=""),
            level=self.scheduler_config.log_level,
            format="{time:HH:mm:ss} | {level} | {message}"
        )
    
    def run_assign_appointments(self) -> dict:
        """Run the AssignAppointment job."""
        try:
            logger.info("Starting AssignAppointment job...")
            result = self.assign_job.execute()
            logger.info(f"AssignAppointment job completed: {result.get('success', False)}")
            return result
        except Exception as e:
            logger.error(f"AssignAppointment job failed: {e}")
            raise
    
    def run_day_plan(self, target_date: Optional[date] = None) -> dict:
        """Run the DayPlan job for a specific date."""
        try:
            logger.info(f"Starting DayPlan job for {target_date or date.today()}...")
            result = self.day_plan_job.execute(target_date)
            logger.info(f"DayPlan job completed: {result.get('success', False)}")
            return result
        except Exception as e:
            logger.error(f"DayPlan job failed: {e}")
            raise
    
    def run_today_day_plan(self) -> dict:
        """Run the DayPlan job for today."""
        return self.run_day_plan(date.today())
    
    def run_tomorrow_day_plan(self) -> dict:
        """Run the DayPlan job for tomorrow."""
        return self.run_day_plan(date.today() + timedelta(days=1))
    
    def setup_schedule(self):
        """Setup the job schedule."""
        # AssignAppointment job runs nightly at 2 AM
        schedule.every().day.at("02:00").do(self.run_assign_appointments)
        
        # DayPlan job runs daily at 6 AM for the current day
        schedule.every().day.at("06:00").do(self.run_today_day_plan)
        
        logger.info("Schedule setup complete:")
        logger.info("  - AssignAppointment job: Daily at 02:00")
        logger.info("  - DayPlan job: Daily at 06:00")
    
    def run_daemon(self):
        """Run the scheduler as a daemon."""
        self._running = True
        self.setup_schedule()
        
        logger.info("Starting scheduler daemon...")
        logger.info("Press Ctrl+C to stop")
        
        try:
            while self._running:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Scheduler daemon stopped by user")
        except Exception as e:
            logger.error(f"Scheduler daemon error: {e}")
            raise
        finally:
            self._running = False
    
    def run_once(self, job_type: str = "assign", target_date: Optional[date] = None) -> dict:
        """
        Run a single job once.
        
        Args:
            job_type: "assign" for AssignAppointment, "dayplan" for DayPlan
            target_date: Target date for DayPlan job (optional)
        """
        if job_type.lower() == "assign":
            return self.run_assign_appointments()
        elif job_type.lower() == "dayplan":
            return self.run_day_plan(target_date)
        else:
            raise ValueError(f"Unknown job type: {job_type}. Use 'assign' or 'dayplan'")
    
    def stop(self):
        """Stop the scheduler daemon."""
        self._running = False
        logger.info("Scheduler stopped")


def main():
    """Main entry point for the scheduler."""
    parser = argparse.ArgumentParser(description='CAXL Appointment Scheduler')
    parser.add_argument('--config-folder', default='config', help='Configuration folder path')
    parser.add_argument('--mode', choices=['daemon', 'once'], default='daemon', 
                       help='Run mode: daemon (continuous) or once (single execution)')
    parser.add_argument('--job', choices=['assign', 'dayplan'], 
                       help='Job type to run (for once mode)')
    parser.add_argument('--date', help='Target date for dayplan job (YYYY-MM-DD format)')
    
    args = parser.parse_args()
    
    try:
        # Determine daemon mode based on scheduler mode
        daemon_mode = args.mode == 'daemon'
        scheduler = AppointmentScheduler(args.config_folder, daemon_mode=daemon_mode)
        
        if args.mode == 'daemon':
            scheduler.run_daemon()
        elif args.mode == 'once':
            if not args.job:
                logger.error("Error: --job is required for once mode")
                sys.exit(1)
            
            target_date = None
            if args.date:
                target_date = datetime.strptime(args.date, '%Y-%m-%d').date()
            
            result = scheduler.run_once(args.job, target_date)
            
            logger.info("=== Job Results ===")
            logger.info(f"Job Type: {args.job}")
            logger.info(f"Success: {result.get('success', False)}")
            logger.info(f"Processing Time: {result.get('processing_time', 0):.2f}s")
            
            if result.get('batch_result'):
                batch = result['batch_result']
                logger.info(f"Total: {batch.total_appointments}")
                logger.info(f"Assigned: {batch.assigned_appointments}")
                logger.info(f"Unassigned: {batch.unassigned_appointments}")
            
            # Exit after completion in once mode
            logger.info("Job completed in once mode. Exiting...")
            sys.exit(0)
        
    except Exception as e:
        logger.error(f"Scheduler failed: {e}")
        if args.mode == 'once':
            sys.exit(1)
        raise


if __name__ == "__main__":
    main() 