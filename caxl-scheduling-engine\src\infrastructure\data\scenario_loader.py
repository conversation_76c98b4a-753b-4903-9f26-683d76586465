"""
Scenario loader for comprehensive testing of the scheduling engine.

This module provides functionality to load different test scenarios from YAML files
to test various edge cases, constraints, and optimization scenarios.
"""

import os
import yaml
from datetime import date, datetime, time
from typing import Dict, List, Any, Optional
from pathlib import Path
from uuid import uuid4, UUID

from loguru import logger

from src.domain.entities.appointment import (
    AppointmentData, AppointmentTiming, AppointmentRelationships, AppointmentPinning
)
from src.domain.entities.provider import (
    Provider, ProviderAvailability, ProviderCapacity, ProviderPreferences, ShiftPattern
)
from src.domain.entities.consumer import Consumer, ConsumerPreferences
from src.domain.value_objects.location import Location, Geofence


class ScenarioLoader:
    """Loader for scenario-based test data."""
    
    def __init__(self, scenarios_dir: str = "data/scenarios"):
        """Initialize the scenario loader."""
        self.scenarios_dir = Path(scenarios_dir)
        self.current_scenario = None
        
    def load_scenario(self, scenario_name: str) -> Dict[str, List[Any]]:
        """
        Load a specific scenario from the scenarios directory.
        
        Args:
            scenario_name: Name of the scenario directory
            
        Returns:
            Dictionary containing providers, consumers, and appointments
        """
        scenario_path = self.scenarios_dir / scenario_name
        
        if not scenario_path.exists():
            logger.error(f"Scenario directory not found: {scenario_path}")
            raise FileNotFoundError(f"Scenario directory not found: {scenario_path}")
        
        logger.info(f"Loading scenario: {scenario_name}")
        self.current_scenario = scenario_name
        
        # Load scenario data
        providers = self._load_providers(scenario_path)
        consumers = self._load_consumers(scenario_path)
        appointments = self._load_appointments(scenario_path)
        
        logger.info(f"Scenario loaded: {len(providers)} providers, {len(consumers)} consumers, {len(appointments)} appointments")
        
        return {
            "providers": providers,
            "consumers": consumers,
            "appointments": appointments
        }
    
    def get_available_scenarios(self) -> List[str]:
        """Get list of available scenarios."""
        if not self.scenarios_dir.exists():
            return []
        
        scenarios = []
        for item in self.scenarios_dir.iterdir():
            if item.is_dir() and (item / "appointments.yml").exists():
                scenarios.append(item.name)
        
        return sorted(scenarios)
    
    def get_scenario_info(self, scenario_name: str) -> Dict[str, Any]:
        """Get information about a specific scenario."""
        scenario_path = self.scenarios_dir / scenario_name
        
        info = {
            "name": scenario_name,
            "path": str(scenario_path),
            "exists": scenario_path.exists(),
            "files": []
        }
        
        if scenario_path.exists():
            for file_path in scenario_path.iterdir():
                if file_path.is_file():
                    info["files"].append(file_path.name)
            
            # Check for README
            readme_path = scenario_path / "README.md"
            if readme_path.exists():
                info["has_readme"] = True
            else:
                info["has_readme"] = False
        
        return info
    
    def _load_providers(self, scenario_path: Path) -> List[Provider]:
        """Load providers from scenario YAML file."""
        providers_file = scenario_path / "providers.yml"
        
        if not providers_file.exists():
            logger.warning(f"Providers file not found: {providers_file}")
            return []
        
        with open(providers_file, 'r') as f:
            data = yaml.safe_load(f)
        
        providers = []
        for provider_data in data.get('providers', []):
            provider = self._create_provider_from_data(provider_data)
            providers.append(provider)
        
        return providers
    
    def _load_consumers(self, scenario_path: Path) -> List[Consumer]:
        """Load consumers from scenario YAML file."""
        consumers_file = scenario_path / "consumers.yml"
        
        if not consumers_file.exists():
            logger.warning(f"Consumers file not found: {consumers_file}")
            return []
        
        with open(consumers_file, 'r') as f:
            data = yaml.safe_load(f)
        
        consumers = []
        for consumer_data in data.get('consumers', []):
            consumer = self._create_consumer_from_data(consumer_data)
            consumers.append(consumer)
        
        return consumers
    
    def _load_appointments(self, scenario_path: Path) -> List[AppointmentData]:
        """Load appointments from scenario YAML file."""
        appointments_file = scenario_path / "appointments.yml"
        
        if not appointments_file.exists():
            logger.warning(f"Appointments file not found: {appointments_file}")
            return []
        
        with open(appointments_file, 'r') as f:
            data = yaml.safe_load(f)
        
        appointments = []
        for appointment_data in data.get('appointments', []):
            appointment = self._create_appointment_from_data(appointment_data)
            appointments.append(appointment)
        
        return appointments
    
    def _create_provider_from_data(self, data: Dict[str, Any]) -> Provider:
        """Create a Provider entity from YAML data."""
        # Parse location
        location = None
        if 'home_location' in data:
            location = Location(**data['home_location'])
        
        # Parse service areas
        service_areas = []
        for area_data in data.get('service_areas', []):
            service_areas.append(Geofence(**area_data))
        
        # Parse availability
        availability = None
        if 'availability' in data:
            availability_data = data['availability']
            
            # Parse primary shift
            primary_shift = None
            if 'primary_shift' in availability_data:
                shift_data = availability_data['primary_shift']
                primary_shift = ShiftPattern(
                    shift_name=shift_data.get('shift_name', 'default'),
                    shift_start=time.fromisoformat(shift_data['shift_start']),
                    shift_end=time.fromisoformat(shift_data['shift_end']),
                    crosses_midnight=shift_data.get('crosses_midnight', False),
                    shift_days=shift_data.get('shift_days', [])
                )
            
            availability = ProviderAvailability(
                primary_shift=primary_shift,
                working_days=availability_data.get('working_days', []),
                working_hours=tuple(time.fromisoformat(h) for h in availability_data['working_hours']) if 'working_hours' in availability_data else None,
                max_hours_per_day=availability_data.get('max_hours_per_day', 8),
                max_hours_per_week=availability_data.get('max_hours_per_week', 40)
            )
        
        # Parse capacity
        capacity = ProviderCapacity()
        if 'capacity' in data:
            capacity_data = data['capacity']
            capacity = ProviderCapacity(
                max_allocated_task_points_in_day=capacity_data.get('max_allocated_task_points_in_day', 27),
                max_tasks_count_in_day=capacity_data.get('max_tasks_count_in_day', 6),
                max_hours_per_day=capacity_data.get('max_hours_per_day', 8)
            )
        
        # Parse preferences
        preferences = ProviderPreferences()
        if 'provider_preferences' in data:
            pref_data = data['provider_preferences']
            preferences = ProviderPreferences(
                blacklisted_consumers=pref_data.get('blacklisted_consumers', []),
                preferred_consumers=pref_data.get('preferred_consumers', []),
                blackout_areas=pref_data.get('blackout_areas', []),
                preferred_task_types=pref_data.get('preferred_task_types', []),
                blacklisted_task_types=pref_data.get('blacklisted_task_types', [])
            )
        
        return Provider(
            id=UUID(data['id']) if isinstance(data['id'], str) else data['id'],
            name=data['name'],
            home_location=location,
            service_areas=service_areas,
            languages=data.get('languages', []),
            transportation=data.get('transportation'),
            availability=availability,
            role=data.get('role'),
            skills=data.get('skills', []),
            capacity=capacity,
            provider_preferences=preferences,
            properties=data.get('properties', {})
        )
    
    def _create_consumer_from_data(self, data: Dict[str, Any]) -> Consumer:
        """Create a Consumer entity from YAML data."""
        # Parse location
        location = None
        if 'location' in data:
            location = Location(**data['location'])
        
        # Parse preferences
        preferences = ConsumerPreferences()
        if 'consumer_preferences' in data:
            pref_data = data['consumer_preferences']
            preferences = ConsumerPreferences(
                preferred_days=pref_data.get('preferred_days', []),
                preferred_hours=tuple(time.fromisoformat(h) for h in pref_data['preferred_hours']) if 'preferred_hours' in pref_data else None,
                unavailable_days=pref_data.get('unavailable_days', []),
                cultural_considerations=pref_data.get('cultural_considerations', []),
                language=pref_data.get('language'),
                gender_preference=pref_data.get('gender_preference'),
                preferred_providers=pref_data.get('preferred_providers', [])
            )
        
        return Consumer(
            id=UUID(data['id']) if isinstance(data['id'], str) else data['id'],
            name=data['name'],
            location=location,
            care_episode_id=data.get('care_episode_id'),
            consumer_preferences=preferences,
            properties=data.get('properties', {})
        )
    
    def _create_appointment_from_data(self, data: Dict[str, Any]) -> AppointmentData:
        """Create an AppointmentData entity from YAML data."""
        # Parse location
        location = None
        if 'location' in data:
            location = Location(**data['location'])
        
        # Parse timing
        timing = AppointmentTiming()
        if 'timing' in data:
            timing_data = data['timing']
            timing = AppointmentTiming(
                is_timed_visit=timing_data.get('is_timed_visit', False),
                preferred_time=time.fromisoformat(timing_data['preferred_time']) if 'preferred_time' in timing_data else None,
                time_flexibility_minutes=timing_data.get('time_flexibility_minutes', 15)
            )
        
        # Parse relationships
        relationships = AppointmentRelationships()
        if 'relationships' in data:
            rel_data = data['relationships']
            relationships = AppointmentRelationships(
                care_episode_id=rel_data.get('care_episode_id'),
                related_appointment_ids=rel_data.get('related_appointment_ids', []),
                prerequisite_appointment_ids=rel_data.get('prerequisite_appointment_ids', []),
                sequence_order=rel_data.get('sequence_order'),
                same_provider_required=rel_data.get('same_provider_required', False)
            )
        
        # Parse pinning
        pinning = AppointmentPinning()
        if 'pinning' in data:
            pin_data = data['pinning']
            pinning = AppointmentPinning(
                is_pinned=pin_data.get('is_pinned', False),
                pin_provider=pin_data.get('pin_provider', False),
                pin_date=pin_data.get('pin_date', False),
                pin_time=pin_data.get('pin_time', False),
                pin_reason=pin_data.get('pin_reason')
            )
        
        return AppointmentData(
            id=UUID(data['id']) if isinstance(data['id'], str) else data['id'],
            consumer_id=UUID(data['consumer_id']) if isinstance(data['consumer_id'], str) else data['consumer_id'],
            appointment_date=date.fromisoformat(data['appointment_date']) if isinstance(data['appointment_date'], str) else data['appointment_date'],
            required_skills=data.get('required_skills', []),
            duration_min=data.get('duration_min', 60),
            urgent=data.get('urgent', False),
            active=data.get('active', True),
            status=data.get('status', 'PENDING_TO_ASSIGN'),
            location=location,
            priority=data.get('priority', 'normal'),
            task_points=data.get('task_points'),
            required_role=data.get('required_role'),
            timing=timing,
            relationships=relationships,
            pinning=pinning,
            properties=data.get('properties', {})
        )


# Global scenario loader instance
scenario_loader = ScenarioLoader()


def load_scenario(scenario_name: str) -> Dict[str, List[Any]]:
    """Load a specific scenario."""
    return scenario_loader.load_scenario(scenario_name)


def get_available_scenarios() -> List[str]:
    """Get list of available scenarios."""
    return scenario_loader.get_available_scenarios()


def get_scenario_info(scenario_name: str) -> Dict[str, Any]:
    """Get information about a specific scenario."""
    return scenario_loader.get_scenario_info(scenario_name) 