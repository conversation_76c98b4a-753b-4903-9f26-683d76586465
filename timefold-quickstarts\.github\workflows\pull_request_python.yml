name: Python

on:
  push:
    branches: [stable, development, '*.x']
    paths:
      - 'python/**/*'
      - '.github/**/*.yml'
  pull_request:
    branches: [stable, development, '*.x']
    paths:
      - 'python/**/*'
      - '.github/**/*.yml'

jobs:
  build:
    concurrency:
      group: pull_request_python-${{ github.event_name }}-${{ github.head_ref }}-${{ matrix.os }}-${{ matrix.java-version }}-${{ matrix.python-version }}
      cancel-in-progress: true
    runs-on: ${{matrix.os}}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-latest]
        java-version: [ 17 ] # Only the first supported LTS; already too many jobs here.
        # TODO: Add Python 3.10 once employee scheduling and school timetabling support it
        python-version: ['3.11', '3.12']
    timeout-minutes: 120
    steps:
      - name: Checkout timefold-quickstarts
        uses: actions/checkout@v4
        with:
          path: './timefold-quickstarts'

      - name: Checkout timefold-solver (PR) # Checkout the PR branch first, if it exists
        if: github.head_ref # Only true if this is a PR.
        id: checkout-solver-pr
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          repository: ${{ github.actor }}/timefold-solver
          ref: ${{ github.head_ref }}
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.
      - name: Checkout timefold-solver (main) # Checkout the main branch if the PR branch does not exist
        if: ${{ steps.checkout-solver-pr.outcome != 'success' }}
        uses: actions/checkout@v4
        with:
          repository: TimefoldAI/timefold-solver
          ref: main
          path: ./timefold-solver
          fetch-depth: 0 # Otherwise merge will fail on account of not having history.

      # Build and test
      - name: "Setup Java and Maven"
        uses: actions/setup-java@v3
        with:
          java-version: ${{matrix.java-version}}
          distribution: 'temurin'
          cache: 'maven'

      - name: Python Setup
        uses: actions/setup-python@v5
        with:
          python-version: ${{matrix.python-version}}
          cache: 'pip'
          cache-dependency-path: |
            **/pyproject.toml

      - name: Install build
        run:
          pip install build

      - name: Build timefold for python
        working-directory: ./timefold-solver
        run: python -m build

      - name: Build and test timefold-quickstarts
        working-directory: ./timefold-quickstarts
        env:
          TIMEFOLD_SOLVER_PYTHON_DIST: "${{ github.workspace }}/timefold-solver/dist"
        run: .github/scripts/run_python_tests.sh
