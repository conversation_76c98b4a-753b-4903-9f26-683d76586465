plugins {
    id "java"
    id "io.quarkus" version "3.23.2"
}

def quarkusVersion = "3.23.2"
def timefoldVersion = "1.23.0"
def profile = System.properties['profile'] ?: ''
def enterprise = System.properties['enterprise'] ?: ''

group = "org.acme"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
    mavenLocal()
}

dependencies {

    implementation enforcedPlatform("io.quarkus:quarkus-bom:${quarkusVersion}")
    implementation platform("ai.timefold.solver:timefold-solver-bom:${timefoldVersion}")
    if (enterprise == 'true') {
        implementation platform("ai.timefold.solver.enterprise:timefold-solver-enterprise-bom:${timefoldVersion}")
        implementation "ai.timefold.solver.enterprise:timefold-solver-enterprise-quarkus"
    }

    implementation "io.quarkus:quarkus-rest"
    implementation "io.quarkus:quarkus-rest-jackson"
    implementation "io.quarkus:quarkus-smallrye-openapi"
    implementation "ai.timefold.solver:timefold-solver-quarkus"
    implementation "ai.timefold.solver:timefold-solver-quarkus-jackson"

    // Testing
    testImplementation "io.quarkus:quarkus-junit5"
    testImplementation "io.quarkus:quarkus-junit5-internal"
    testImplementation "io.rest-assured:rest-assured"
    testImplementation "ai.timefold.solver:timefold-solver-test"
    testImplementation "org.awaitility:awaitility"
    testImplementation "org.assertj:assertj-core:3.27.3"

    // UI
    implementation "io.quarkus:quarkus-webjars-locator"
    runtimeOnly "ai.timefold.solver:timefold-solver-webui"
    runtimeOnly "org.webjars:bootstrap:5.2.3"
    runtimeOnly "org.webjars:jquery:3.6.4"
    runtimeOnly "org.webjars:font-awesome:5.15.1"
    runtimeOnly "org.webjars.npm:js-joda:1.11.0"
}

java {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

compileJava {
    options.encoding = "UTF-8"
    options.compilerArgs << "-parameters"
}

compileTestJava {
    options.encoding = "UTF-8"
}

test {
    systemProperty "java.util.logging.manager", "org.jboss.logmanager.LogManager"
    // Gradle needs native tests in src/native-test/java, but maven needs them in src/test/java instead.
    // Maven first, so we skip them in Gradle unfortunately.
    exclude "**/**IT.class"
    // Log the test execution results.
    testLogging {
        events "passed", "skipped", "failed"
    }

    if (profile == 'slowly') {
        useJUnitPlatform()
    } else {
        useJUnitPlatform {
            excludeTags "slowly"
        }
    }
}
