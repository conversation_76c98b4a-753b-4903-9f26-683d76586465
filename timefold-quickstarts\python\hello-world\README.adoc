= School Timetabling (Python)

Assign lessons to timeslots and rooms to produce a better schedule for teachers and students.

== Prerequisites

. Install https://www.python.org/downloads/[Python 3.10+]

. Install JDK 17+, for example with https://sdkman.io[Sdkman]:
+
----
$ sdk install java
----

== Run the application

. Git clone the timefold-quickstarts repo:
+
[source, shell]
----
$ git clone https://github.com/TimefoldAI/timefold-quickstarts.git
...
$ cd timefold-quickstarts/python/hello-world
----

. Create a virtual environment
+
[source, shell]
----
$ python -m venv .venv
----

. Activate the virtual environment
+
[source, shell]
----
$ . .venv/bin/activate
----

. Install the application
+
[source, shell]
----
$ pip install -e .
----

. Run the application
+
[source, shell]
----
$ run-app
----

. Run tests
+
[source, shell]
----
$ pytest
----

== More information

Visit https://timefold.ai[timefold.ai].
